{% extends "base.html" %}

{% block title %}إدارة الضامنين - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-user-shield me-2 text-success"></i>
                إدارة الضامنين
            </h2>
            <button class="btn btn-success btn-lg" data-bs-toggle="modal" data-bs-target="#addGuarantorModal">
                <i class="fas fa-plus me-2"></i>
                إضافة ضامن جديد
            </button>
        </div>
    </div>
</div>

<!-- شريط البحث والفلاتر -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card filter-section">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-8">
                        <div class="search-box">
                            <input type="text" class="form-control" id="guarantorSearch" placeholder="البحث في الضامنين...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-secondary" onclick="exportTableToPDF('guarantorsTable', 'قائمة الضامنين')">
                                <i class="fas fa-file-pdf me-1"></i>
                                PDF
                            </button>
                            <button class="btn btn-outline-secondary" onclick="printTable('guarantorsTable')">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الضامنين -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="guarantorsTable">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم الضامن</th>
                                <th>رقم الهاتف</th>
                                <th>العنوان</th>
                                <th>رقم الهوية</th>
                                <th>عدد العملاء</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for guarantor in guarantors %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    <strong>{{ guarantor.name }}</strong>
                                </td>
                                <td>
                                    <i class="fas fa-phone me-1 text-muted"></i>
                                    {{ guarantor.phone or 'غير محدد' }}
                                </td>
                                <td>{{ guarantor.address or 'غير محدد' }}</td>
                                <td>{{ guarantor.national_id or 'غير محدد' }}</td>
                                <td>
                                    <span class="badge bg-info">0 عميل</span>
                                </td>
                                <td>{{ guarantor.created_at[:10] }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewGuarantor({{ guarantor.id }})" 
                                                data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="editGuarantor({{ guarantor.id }})"
                                                data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteGuarantor({{ guarantor.id }})"
                                                data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center text-muted py-4">
                                    <i class="fas fa-user-shield fa-3x mb-3 d-block"></i>
                                    لا توجد ضامنين مسجلين حتى الآن
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إضافة ضامن جديد -->
<div class="modal fade" id="addGuarantorModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-shield me-2"></i>
                    إضافة ضامن جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addGuarantorForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">اسم الضامن *</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                        <div class="col-12">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" name="address" rows="2"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهوية</label>
                            <input type="text" class="form-control" name="national_id">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="saveGuarantor()">
                    <i class="fas fa-save me-2"></i>
                    حفظ الضامن
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تفعيل البحث في الجدول
searchTable('guarantorSearch', 'guarantorsTable');

// حفظ ضامن جديد
async function saveGuarantor() {
    if (!validateForm('addGuarantorForm')) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    const form = document.getElementById('addGuarantorForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    const result = await sendData('/api/guarantors', data);
    if (result) {
        bootstrap.Modal.getInstance(document.getElementById('addGuarantorModal')).hide();
        clearForm('addGuarantorForm');
        location.reload();
    }
}

// عرض تفاصيل الضامن
async function viewGuarantor(guarantorId) {
    try {
        const response = await fetch(`/api/guarantors/${guarantorId}`);
        const data = await response.json();

        if (data.success) {
            const guarantor = data.guarantor;
            const details = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>الاسم:</strong> ${guarantor.name}</p>
                        <p><strong>رقم الهاتف:</strong> ${guarantor.phone || 'غير محدد'}</p>
                        <p><strong>رقم الهوية:</strong> ${guarantor.national_id || 'غير محدد'}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>العنوان:</strong> ${guarantor.address || 'غير محدد'}</p>
                        <p><strong>تاريخ الإضافة:</strong> ${guarantor.created_at}</p>
                    </div>
                </div>
            `;

            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل الضامن</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">${details}</div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        } else {
            showAlert(data.message, 'error');
        }
    } catch (error) {
        showAlert('خطأ في جلب بيانات الضامن', 'error');
    }
}

// تعديل الضامن
async function editGuarantor(guarantorId) {
    try {
        const response = await fetch(`/api/guarantors/${guarantorId}`);
        const data = await response.json();

        if (data.success) {
            const guarantor = data.guarantor;

            document.querySelector('#addGuarantorForm input[name="name"]').value = guarantor.name;
            document.querySelector('#addGuarantorForm input[name="phone"]').value = guarantor.phone || '';
            document.querySelector('#addGuarantorForm textarea[name="address"]').value = guarantor.address || '';
            document.querySelector('#addGuarantorForm input[name="national_id"]').value = guarantor.national_id || '';

            document.querySelector('#addGuarantorModal .modal-title').innerHTML = '<i class="fas fa-edit me-2"></i>تعديل بيانات الضامن';

            const saveButton = document.querySelector('#addGuarantorModal .btn-success');
            saveButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ التعديلات';
            saveButton.onclick = () => updateGuarantor(guarantorId);

            const modal = new bootstrap.Modal(document.getElementById('addGuarantorModal'));
            modal.show();
        } else {
            showAlert(data.message, 'error');
        }
    } catch (error) {
        showAlert('خطأ في جلب بيانات الضامن', 'error');
    }
}

// تحديث بيانات الضامن
async function updateGuarantor(guarantorId) {
    if (!validateForm('addGuarantorForm')) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    const form = document.getElementById('addGuarantorForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    try {
        const response = await fetch(`/api/guarantors/${guarantorId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            showAlert(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addGuarantorModal')).hide();
            resetGuarantorForm();
            location.reload();
        } else {
            showAlert(result.message, 'error');
        }
    } catch (error) {
        showAlert('خطأ في تحديث بيانات الضامن', 'error');
    }
}

// حذف الضامن
async function deleteGuarantor(guarantorId) {
    if (confirm('هل أنت متأكد من حذف هذا الضامن؟\nسيتم حذف جميع البيانات المرتبطة به نهائياً.')) {
        try {
            const response = await fetch(`/api/guarantors/${guarantorId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                showAlert(result.message, 'success');
                location.reload();
            } else {
                showAlert(result.message, 'error');
            }
        } catch (error) {
            showAlert('خطأ في حذف الضامن', 'error');
        }
    }
}

// إعادة تعيين نموذج الضامن
function resetGuarantorForm() {
    document.querySelector('#addGuarantorModal .modal-title').innerHTML = '<i class="fas fa-user-shield me-2"></i>إضافة ضامن جديد';
    const saveButton = document.querySelector('#addGuarantorModal .btn-success');
    saveButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ الضامن';
    saveButton.onclick = saveGuarantor;
    clearForm('addGuarantorForm');
}

// إعادة تعيين النموذج عند إغلاق المودال
document.getElementById('addGuarantorModal').addEventListener('hidden.bs.modal', function () {
    resetGuarantorForm();
});
</script>
{% endblock %}
