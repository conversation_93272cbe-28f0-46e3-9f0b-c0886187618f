# 🏍️ نظام محاسبة مبيعات الدراجات النارية - تطبيق سطح المكتب

## ✨ تصميم جديد وجميل!

تم تحديث التطبيق بتصميم حديث وجذاب مع:
- 🎨 **ألوان عصرية ومتدرجة**
- 🖼️ **واجهة جميلة مع أيقونات**
- 💫 **تأثيرات بصرية رائعة**
- 🌈 **ألوان متناوبة في الجداول**
- 🎯 **أزرار تفاعلية مع تأثيرات الهوفر**

## 🖥️ تطبيق سطح المكتب بدلاً من المتصفح

تم تصميم هذا التطبيق ليعمل كبرنامج سطح مكتب مستقل بدون الحاجة لمتصفح الويب.

## 🎯 المميزات الرئيسية:

### ✨ **التصميم الجديد والجميل:**
- 🎨 **خلفية متدرجة** بألوان عصرية
- 🖼️ **أيقونات جميلة** لكل وظيفة
- 🌈 **ألوان متناوبة** في الجداول
- 💫 **تأثيرات بصرية** رائعة
- 🎯 **أزرار تفاعلية** مع تأثيرات الهوفر
- 📱 **تصميم حديث** يشبه التطبيقات العصرية

### ✅ **واجهة سطح المكتب:**
- تطبيق مستقل لا يحتاج متصفح
- واجهة عربية بالكامل مع تصميم جميل
- تصميم عصري وجذاب
- نوافذ منفصلة لكل وظيفة مع تصميم متطور

### ✅ **إدارة العملاء:**
- إضافة عملاء جدد
- تعديل بيانات العملاء
- حذف العملاء (مع التحقق من الفواتير المرتبطة)
- ربط العملاء بالضامنين

### ✅ **إدارة الفواتير:**
- إنشاء فواتير جديدة
- عرض جميع الفواتير
- تفاصيل كاملة لكل فاتورة
- حساب تلقائي للأقساط

### ✅ **نوع الدراجة المرن:**
- كتابة حرة لنوع الدراجة
- إنشاء أنواع جديدة تلقائياً
- لا توجد قيود على الأسماء

### ✅ **قاعدة بيانات محلية:**
- SQLite محلية
- لا حاجة لخادم
- بيانات آمنة على الجهاز

## 🚀 كيفية التشغيل:

### الطريقة السريعة:
```bash
python run_desktop.py
```

### الطريقة المباشرة:
```bash
python desktop_app.py
```

## 📋 متطلبات التشغيل:

### المطلوب:
- **Python 3.6+** (مثبت مسبقاً على معظم الأنظمة)
- **Tkinter** (مدمج مع Python)
- **SQLite** (مدمج مع Python)

### لا يحتاج:
- ❌ متصفح ويب
- ❌ خادم ويب
- ❌ اتصال بالإنترنت
- ❌ مكتبات إضافية

## 📁 هيكل الملفات:

```
📦 نظام محاسبة الدراجات النارية
├── 📄 desktop_app.py          # التطبيق الرئيسي
├── 📄 dialogs.py              # نوافذ الحوار
├── 📄 run_desktop.py          # ملف التشغيل
├── 📄 motorcycle_accounting.db # قاعدة البيانات (تُنشأ تلقائياً)
└── 📄 README_Desktop.md       # هذا الملف
```

## 🎮 كيفية الاستخدام:

### 1. **الشاشة الرئيسية:**
- عرض الإحصائيات السريعة
- آخر الفواتير
- أزرار الوصول السريع

### 2. **إدارة العملاء:**
- اضغط "إدارة العملاء"
- أضف عميل جديد بالضغط على "إضافة عميل جديد"
- املأ البيانات واحفظ

### 3. **إنشاء فاتورة:**
- اضغط "إدارة الفواتير"
- اضغط "إنشاء فاتورة جديدة"
- اختر العميل
- اكتب نوع الدراجة (مثال: "BMW - S1000RR")
- أدخل المبلغ والتفاصيل
- احفظ الفاتورة

### 4. **عرض التفاصيل:**
- اختر أي فاتورة من الجدول
- اضغط "عرض التفاصيل"
- ستظهر نافذة بجميع المعلومات

## 🔧 المميزات التقنية:

### ✅ **قاعدة البيانات:**
- SQLite محلية وآمنة
- إنشاء تلقائي للجداول
- بيانات تجريبية للاختبار
- نسخ احتياطي سهل

### ✅ **الواجهة:**
- Tkinter الأصلي
- خطوط عربية واضحة
- ألوان مريحة للعين
- تخطيط منظم

### ✅ **الأمان:**
- التحقق من البيانات
- منع الحذف الخاطئ
- رسائل تأكيد واضحة
- معالجة الأخطاء

## 🎯 الوظائف المتاحة حالياً:

### ✅ **مكتملة:**
- إدارة العملاء (إضافة، تعديل، حذف)
- إنشاء الفواتير
- عرض تفاصيل الفواتير
- الإحصائيات السريعة
- قاعدة البيانات المحلية

### 🔄 **قيد التطوير:**
- تعديل الفواتير
- سندات القبض
- التقارير المفصلة
- الإعدادات المتقدمة
- طباعة الفواتير

## 🐛 استكشاف الأخطاء:

### إذا لم يعمل التطبيق:

1. **تأكد من Python:**
```bash
python --version
```

2. **تأكد من الملفات:**
- desktop_app.py
- dialogs.py

3. **تشغيل مع تفاصيل الأخطاء:**
```bash
python -u desktop_app.py
```

### الأخطاء الشائعة:

- **"No module named 'tkinter'"**: تثبيت Python كامل
- **"Permission denied"**: تشغيل كمدير
- **"File not found"**: التأكد من المجلد الصحيح

## 🎉 المزايا مقارنة بتطبيق الويب:

### ✅ **أسرع:**
- لا حاجة لتحميل صفحات
- استجابة فورية
- لا انتظار للخادم

### ✅ **أبسط:**
- نقرة واحدة للتشغيل
- لا إعدادات معقدة
- لا مشاكل متصفح

### ✅ **أكثر أماناً:**
- البيانات محلية
- لا تسريب عبر الشبكة
- تحكم كامل

### ✅ **أكثر استقراراً:**
- لا يتأثر بتحديثات المتصفح
- لا مشاكل JavaScript
- أداء ثابت

## 📞 الدعم:

إذا واجهت أي مشكلة:
1. تأكد من متطلبات التشغيل
2. اقرأ رسائل الخطأ بعناية
3. جرب إعادة تشغيل التطبيق
4. تحقق من صلاحيات الملفات

---

**🎯 الآن يمكنك استخدام نظام محاسبة الدراجات النارية كتطبيق سطح مكتب مستقل!**
