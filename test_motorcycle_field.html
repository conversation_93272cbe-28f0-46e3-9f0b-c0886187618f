<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حقل نوع الدراجة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section {
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            background: #f8f9ff;
        }
        .success-msg {
            color: #28a745;
            font-weight: bold;
        }
        .error-msg {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2 class="text-center mb-4">🧪 اختبار حقل نوع الدراجة القابل للكتابة</h2>
        
        <div class="test-section">
            <h4><i class="fas fa-motorcycle me-2"></i>حقل نوع الدراجة</h4>
            
            <div class="row">
                <div class="col-md-8">
                    <label class="form-label">نوع الدراجة *</label>
                    <div class="input-group" id="motorcycleTextGroup">
                        <input type="text" class="form-control" name="motorcycle_type_text" 
                               placeholder="اكتب نوع الدراجة..." list="motorcycleList" required
                               oninput="updatePriceFromText()">
                        <button class="btn btn-outline-secondary" type="button" onclick="toggleMotorcycleSelect()" 
                                data-bs-toggle="tooltip" title="التبديل إلى القائمة المنسدلة">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                    <datalist id="motorcycleList">
                        <option value="هوندا - CBR 150" data-id="1" data-price="25000">
                        <option value="ياماها - YZF-R15" data-id="2" data-price="28000">
                        <option value="سوزوكي - GSX-R125" data-id="3" data-price="22000">
                        <option value="كاواساكي - Ninja 250" data-id="4" data-price="35000">
                        <option value="BMW - S1000RR" data-id="5" data-price="85000">
                    </datalist>
                    <select class="form-select d-none" name="motorcycle_type_id" onchange="updatePriceFromSelect()">
                        <option value="">اختر نوع الدراجة</option>
                        <option value="1" data-price="25000">هوندا - CBR 150</option>
                        <option value="2" data-price="28000">ياماها - YZF-R15</option>
                        <option value="3" data-price="22000">سوزوكي - GSX-R125</option>
                        <option value="4" data-price="35000">كاواساكي - Ninja 250</option>
                        <option value="5" data-price="85000">BMW - S1000RR</option>
                    </select>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        يمكنك الكتابة مباشرة أو الضغط على <i class="fas fa-list"></i> للاختيار من القائمة
                    </small>
                </div>
                
                <div class="col-md-4">
                    <label class="form-label">المبلغ الإجمالي</label>
                    <div class="input-group">
                        <input type="number" class="form-control" name="total_amount" 
                               placeholder="أدخل المبلغ..." readonly>
                        <span class="input-group-text">ر.س</span>
                    </div>
                    <small class="text-muted">سيتم تحديثه تلقائياً</small>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h4><i class="fas fa-clipboard-check me-2"></i>نتائج الاختبار</h4>
            <div id="testResults" style="height: 300px; overflow-y: auto; background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;">
                <p class="text-muted">ابدأ بالكتابة في حقل نوع الدراجة لرؤية النتائج...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h4><i class="fas fa-tasks me-2"></i>خطوات الاختبار</h4>
            <div class="row">
                <div class="col-md-6">
                    <h6>اختبار الكتابة المباشرة:</h6>
                    <ol>
                        <li>اكتب "BMW - S1000RR" في الحقل</li>
                        <li>لاحظ تحديث المبلغ تلقائياً</li>
                        <li>اكتب نوع دراجة جديد مثل "دوكاتي - Panigale V4"</li>
                        <li>تأكد من إمكانية الكتابة بحرية</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h6>اختبار التبديل:</h6>
                    <ol>
                        <li>اضغط على زر <i class="fas fa-list"></i> للتبديل</li>
                        <li>اختر من القائمة المنسدلة</li>
                        <li>اضغط على زر <i class="fas fa-keyboard"></i> للعودة</li>
                        <li>تأكد من سلاسة التبديل</li>
                    </ol>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="http://localhost:5000/invoices" target="_blank" class="btn btn-primary btn-lg">
                <i class="fas fa-external-link-alt me-2"></i>
                اختبار في النظام الفعلي
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colorClass = {
                'info': 'text-primary',
                'success': 'success-msg',
                'error': 'error-msg',
                'warning': 'text-warning'
            }[type] || 'text-dark';
            
            resultsDiv.innerHTML += `<div class="${colorClass}"><strong>[${timestamp}]</strong> ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // تبديل بين حقل الكتابة والقائمة المنسدلة
        function toggleMotorcycleSelect() {
            const textGroup = document.getElementById('motorcycleTextGroup');
            const selectInput = document.querySelector('select[name="motorcycle_type_id"]');
            const toggleButton = textGroup.querySelector('button');
            const toggleIcon = toggleButton.querySelector('i');
            
            if (selectInput.classList.contains('d-none')) {
                // إظهار القائمة المنسدلة وإخفاء حقل الكتابة
                selectInput.classList.remove('d-none');
                textGroup.classList.add('d-none');
                
                // تغيير أيقونة الزر
                toggleIcon.className = 'fas fa-keyboard';
                toggleButton.setAttribute('title', 'التبديل إلى حقل الكتابة');
                
                log('✅ تم التبديل إلى القائمة المنسدلة', 'success');
            } else {
                // إظهار حقل الكتابة وإخفاء القائمة المنسدلة
                selectInput.classList.add('d-none');
                textGroup.classList.remove('d-none');
                
                // تغيير أيقونة الزر
                toggleIcon.className = 'fas fa-list';
                toggleButton.setAttribute('title', 'التبديل إلى القائمة المنسدلة');
                
                log('✅ تم التبديل إلى حقل الكتابة', 'success');
            }
        }

        // تحديث السعر عند اختيار من القائمة المنسدلة
        function updatePriceFromSelect() {
            const select = document.querySelector('select[name="motorcycle_type_id"]');
            const priceInput = document.querySelector('input[name="total_amount"]');
            
            if (select.selectedIndex > 0) {
                const price = select.options[select.selectedIndex].dataset.price;
                const motorcycleName = select.options[select.selectedIndex].text;
                priceInput.value = price;
                log(`💰 تم تحديث السعر من القائمة: ${motorcycleName} - ${price} ر.س`, 'success');
            } else {
                priceInput.value = '';
                log('🔄 تم مسح السعر', 'info');
            }
        }

        // تحديث السعر عند الكتابة في حقل نوع الدراجة
        function updatePriceFromText() {
            const textInput = document.querySelector('input[name="motorcycle_type_text"]');
            const priceInput = document.querySelector('input[name="total_amount"]');
            const datalist = document.getElementById('motorcycleList');
            
            log(`📝 تم الكتابة: "${textInput.value}"`, 'info');
            
            if (textInput.value && datalist) {
                // البحث في قائمة الاقتراحات عن سعر مطابق
                const options = datalist.querySelectorAll('option');
                for (let option of options) {
                    if (option.value === textInput.value) {
                        const price = option.dataset.price;
                        if (price) {
                            priceInput.value = price;
                            log(`💰 تم العثور على سعر مطابق: ${price} ر.س`, 'success');
                            return;
                        }
                    }
                }
                
                // إذا لم يتم العثور على مطابقة
                if (textInput.value.length > 3) {
                    log(`🆕 نوع دراجة جديد: "${textInput.value}" - يمكن إدخال السعر يدوياً`, 'warning');
                }
            }
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            log('🚀 مرحباً بك في اختبار حقل نوع الدراجة!', 'success');
            log('💡 ابدأ بالكتابة في الحقل أعلاه', 'info');
            log('💡 جرب الكتابة من القائمة المقترحة أو اكتب نوع جديد', 'info');
            log('💡 استخدم زر التبديل للتنقل بين الكتابة والقائمة', 'info');
        });

        // مراقبة التغييرات في الحقل
        document.querySelector('input[name="motorcycle_type_text"]').addEventListener('focus', function() {
            log('🎯 تم التركيز على حقل الكتابة', 'info');
        });

        document.querySelector('input[name="motorcycle_type_text"]').addEventListener('blur', function() {
            log('📤 تم ترك حقل الكتابة', 'info');
        });
    </script>
</body>
</html>
