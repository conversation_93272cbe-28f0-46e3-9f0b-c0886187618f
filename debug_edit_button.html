<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر التعديل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>🧪 اختبار زر التعديل في بيانات العملاء</h2>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار API</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="testGetCustomer()">
                            <i class="fas fa-user me-2"></i>
                            اختبار جلب بيانات العميل
                        </button>
                        <br>
                        <button class="btn btn-warning mb-2" onclick="testEditCustomer()">
                            <i class="fas fa-edit me-2"></i>
                            اختبار تعديل العميل
                        </button>
                        <br>
                        <button class="btn btn-success mb-2" onclick="testAddCustomer()">
                            <i class="fas fa-plus me-2"></i>
                            اختبار إضافة عميل
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>نتائج الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                            <p class="text-muted">اضغط على أي زر لبدء الاختبار...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاختبار:</h6>
                    <ol>
                        <li>تأكد من تشغيل الخادم على المنفذ 5000</li>
                        <li>اضغط على أزرار الاختبار أعلاه</li>
                        <li>راقب النتائج في المربع الأيمن</li>
                        <li>إذا نجحت الاختبارات، فزر التعديل يعمل بشكل صحيح</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colorClass = {
                'info': 'text-primary',
                'success': 'text-success',
                'error': 'text-danger',
                'warning': 'text-warning'
            }[type] || 'text-dark';
            
            resultsDiv.innerHTML += `<div class="${colorClass}"><strong>[${timestamp}]</strong> ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function testGetCustomer() {
            log('🔍 اختبار جلب بيانات العميل رقم 1...', 'info');
            
            try {
                const response = await fetch('/api/customers/1');
                log(`📡 حالة الاستجابة: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`📄 البيانات المستلمة: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.success) {
                        log('✅ تم جلب بيانات العميل بنجاح', 'success');
                        log(`👤 اسم العميل: ${data.customer.name}`, 'success');
                    } else {
                        log('❌ فشل في جلب بيانات العميل', 'error');
                    }
                } else {
                    log(`❌ خطأ HTTP: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            }
        }

        async function testEditCustomer() {
            log('✏️ اختبار تعديل بيانات العميل...', 'info');
            
            const testData = {
                name: 'عميل محدث - ' + new Date().toLocaleTimeString('ar-SA'),
                phone: '0501234567',
                address: 'عنوان محدث',
                national_id: '1234567890',
                guarantor_id: ''
            };
            
            try {
                const response = await fetch('/api/customers/1', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                log(`📡 حالة الاستجابة: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`📄 البيانات المستلمة: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.success) {
                        log('✅ تم تحديث بيانات العميل بنجاح', 'success');
                    } else {
                        log('❌ فشل في تحديث بيانات العميل', 'error');
                    }
                } else {
                    log(`❌ خطأ HTTP: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            }
        }

        async function testAddCustomer() {
            log('➕ اختبار إضافة عميل جديد...', 'info');
            
            const testData = {
                name: 'عميل تجريبي - ' + new Date().toLocaleTimeString('ar-SA'),
                phone: '0507654321',
                address: 'عنوان تجريبي',
                national_id: '9876543210',
                guarantor_id: ''
            };
            
            try {
                const response = await fetch('/api/customers', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                log(`📡 حالة الاستجابة: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`📄 البيانات المستلمة: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.success) {
                        log('✅ تم إضافة العميل بنجاح', 'success');
                    } else {
                        log('❌ فشل في إضافة العميل', 'error');
                    }
                } else {
                    log(`❌ خطأ HTTP: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            }
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            log('🚀 بدء اختبار زر التعديل...', 'info');
            log('📋 للاختبار اليدوي، اذهب إلى: http://localhost:5000/customers', 'info');
        });
    </script>
</body>
</html>
