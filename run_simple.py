#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تشغيل النسخة المبسطة من نظام محاسبة مبيعات الدراجات النارية
"""

import sys
import os

def main():
    print("🏍️ نظام محاسبة مبيعات الدراجات النارية - النسخة المبسطة")
    print("=" * 60)
    print("🚀 بدء التشغيل...")
    
    try:
        # اختبار tkinter
        import tkinter as tk
        print("✅ tkinter متوفر")
        
        # اختبار sqlite3
        import sqlite3
        print("✅ sqlite3 متوفر")
        
        # تشغيل التطبيق
        from simple_desktop import main as app_main
        print("✅ التطبيق جاهز للتشغيل")
        print("💡 ستظهر النافذة الرئيسية...")
        print("-" * 60)
        
        app_main()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من تثبيت Python بشكل صحيح")
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🙏 شكراً لاستخدام النظام")
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
