{% extends "base.html" %}

{% block title %}إدارة سندات القبض - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-receipt me-2 text-info"></i>
                إدارة سندات القبض
            </h2>
            <button class="btn btn-info btn-lg" data-bs-toggle="modal" data-bs-target="#addReceiptModal">
                <i class="fas fa-plus me-2"></i>
                إنشاء سند قبض جديد
            </button>
        </div>
    </div>
</div>

<!-- شريط البحث والفلاتر -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card filter-section">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="search-box">
                            <input type="text" class="form-control" id="receiptSearch" placeholder="البحث في سندات القبض...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="paymentMethodFilter">
                            <option value="">جميع طرق الدفع</option>
                            <option value="cash">نقداً</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="check">شيك</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="dateFilter">
                    </div>
                    <div class="col-md-3">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-secondary" onclick="exportTableToPDF('receiptsTable', 'قائمة سندات القبض')">
                                <i class="fas fa-file-pdf me-1"></i>
                                PDF
                            </button>
                            <button class="btn btn-outline-secondary" onclick="printTable('receiptsTable')">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول سندات القبض -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="receiptsTable">
                        <thead>
                            <tr>
                                <th>رقم السند</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>تاريخ الدفع</th>
                                <th>طريقة الدفع</th>
                                <th>ملاحظات</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for receipt in receipts %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ receipt.receipt_number }}</strong>
                                </td>
                                <td>{{ receipt.customer_name }}</td>
                                <td>
                                    <strong class="text-success">{{ receipt.amount|format_currency }}</strong>
                                </td>
                                <td>{{ receipt.payment_date }}</td>
                                <td>
                                    {% if receipt.payment_method == 'cash' %}
                                        <span class="badge bg-success">نقداً</span>
                                    {% elif receipt.payment_method == 'bank_transfer' %}
                                        <span class="badge bg-primary">تحويل بنكي</span>
                                    {% elif receipt.payment_method == 'check' %}
                                        <span class="badge bg-warning">شيك</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ receipt.payment_method }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ receipt.notes or '-' }}</td>
                                <td>{{ receipt.created_at[:10] }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewReceipt({{ receipt.id }})" 
                                                data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="editReceipt({{ receipt.id }})"
                                                data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="printReceipt({{ receipt.id }})"
                                                data-bs-toggle="tooltip" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteReceipt({{ receipt.id }})"
                                                data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center text-muted py-4">
                                    <i class="fas fa-receipt fa-3x mb-3 d-block"></i>
                                    لا توجد سندات قبض مسجلة حتى الآن
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إنشاء سند قبض جديد -->
<div class="modal fade" id="addReceiptModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-receipt me-2"></i>
                    إنشاء سند قبض جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addReceiptForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">العميل *</label>
                            <select class="form-select" name="customer_id" required>
                                <option value="">اختر العميل</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}">{{ customer.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">المبلغ *</label>
                            <input type="number" class="form-control" name="amount" required step="0.01">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ الدفع *</label>
                            <input type="date" class="form-control" name="payment_date" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">طريقة الدفع *</label>
                            <select class="form-select" name="payment_method" required>
                                <option value="">اختر طريقة الدفع</option>
                                <option value="cash">نقداً</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" onclick="saveReceipt()">
                    <i class="fas fa-save me-2"></i>
                    حفظ السند
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تفعيل البحث في الجدول
searchTable('receiptSearch', 'receiptsTable');

// تعيين التاريخ الحالي
document.querySelector('input[name="payment_date"]').value = new Date().toISOString().split('T')[0];

// حفظ سند قبض جديد
async function saveReceipt() {
    if (!validateForm('addReceiptForm')) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    const form = document.getElementById('addReceiptForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // تحويل المبلغ إلى رقم
    data.amount = parseFloat(data.amount);
    
    const result = await sendData('/api/receipts', data);
    if (result) {
        bootstrap.Modal.getInstance(document.getElementById('addReceiptModal')).hide();
        clearForm('addReceiptForm');
        location.reload();
    }
}

// عرض تفاصيل السند
async function viewReceipt(receiptId) {
    try {
        const response = await fetch(`/api/receipts/${receiptId}`);
        const data = await response.json();

        if (data.success) {
            const receipt = data.receipt;
            const paymentMethodText = {
                'cash': 'نقداً',
                'bank_transfer': 'تحويل بنكي',
                'check': 'شيك'
            };

            const details = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>رقم السند:</strong> ${receipt.receipt_number}</p>
                        <p><strong>العميل:</strong> ${receipt.customer_name}</p>
                        <p><strong>المبلغ:</strong> ${formatArabicNumber(receipt.amount)}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>تاريخ الدفع:</strong> ${receipt.payment_date}</p>
                        <p><strong>طريقة الدفع:</strong> ${paymentMethodText[receipt.payment_method] || receipt.payment_method}</p>
                        <p><strong>تاريخ الإنشاء:</strong> ${receipt.created_at}</p>
                    </div>
                    <div class="col-12">
                        <p><strong>ملاحظات:</strong> ${receipt.notes || 'لا توجد ملاحظات'}</p>
                    </div>
                </div>
            `;

            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل سند القبض</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">${details}</div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-info" onclick="printReceipt(${receiptId})">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        } else {
            showAlert(data.message, 'error');
        }
    } catch (error) {
        showAlert('خطأ في جلب بيانات السند', 'error');
    }
}

// تعديل السند
async function editReceipt(receiptId) {
    try {
        const response = await fetch(`/api/receipts/${receiptId}`);
        const data = await response.json();

        if (data.success) {
            const receipt = data.receipt;

            document.querySelector('#addReceiptForm select[name="customer_id"]').value = receipt.customer_id;
            document.querySelector('#addReceiptForm input[name="amount"]').value = receipt.amount;
            document.querySelector('#addReceiptForm input[name="payment_date"]').value = receipt.payment_date;
            document.querySelector('#addReceiptForm select[name="payment_method"]').value = receipt.payment_method;
            document.querySelector('#addReceiptForm textarea[name="notes"]').value = receipt.notes || '';

            document.querySelector('#addReceiptModal .modal-title').innerHTML = '<i class="fas fa-edit me-2"></i>تعديل سند القبض';

            const saveButton = document.querySelector('#addReceiptModal .btn-info');
            saveButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ التعديلات';
            saveButton.onclick = () => updateReceipt(receiptId);

            const modal = new bootstrap.Modal(document.getElementById('addReceiptModal'));
            modal.show();
        } else {
            showAlert(data.message, 'error');
        }
    } catch (error) {
        showAlert('خطأ في جلب بيانات السند', 'error');
    }
}

// تحديث بيانات السند
async function updateReceipt(receiptId) {
    if (!validateForm('addReceiptForm')) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    const form = document.getElementById('addReceiptForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    data.amount = parseFloat(data.amount);

    try {
        const response = await fetch(`/api/receipts/${receiptId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            showAlert(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addReceiptModal')).hide();
            resetReceiptForm();
            location.reload();
        } else {
            showAlert(result.message, 'error');
        }
    } catch (error) {
        showAlert('خطأ في تحديث بيانات السند', 'error');
    }
}

// طباعة السند
function printReceipt(receiptId) {
    fetch(`/api/receipts/${receiptId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const receipt = data.receipt;
                const paymentMethodText = {
                    'cash': 'نقداً',
                    'bank_transfer': 'تحويل بنكي',
                    'check': 'شيك'
                };

                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <!DOCTYPE html>
                    <html dir="rtl" lang="ar">
                    <head>
                        <meta charset="UTF-8">
                        <title>سند قبض - ${receipt.receipt_number}</title>
                        <style>
                            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
                            body {
                                font-family: 'Cairo', Arial, sans-serif;
                                direction: rtl;
                                margin: 20px;
                                font-size: 16px;
                            }
                            .receipt-header {
                                text-align: center;
                                border-bottom: 2px solid #333;
                                padding-bottom: 20px;
                                margin-bottom: 30px;
                            }
                            .receipt-body {
                                margin: 30px 0;
                            }
                            .receipt-row {
                                display: flex;
                                justify-content: space-between;
                                margin: 15px 0;
                                padding: 10px;
                                border-bottom: 1px solid #eee;
                            }
                            .receipt-footer {
                                margin-top: 50px;
                                text-align: center;
                                border-top: 1px solid #333;
                                padding-top: 20px;
                            }
                            @media print { body { margin: 0; } }
                        </style>
                    </head>
                    <body>
                        <div class="receipt-header">
                            <h1>سند قبض</h1>
                            <h2>رقم السند: ${receipt.receipt_number}</h2>
                        </div>

                        <div class="receipt-body">
                            <div class="receipt-row">
                                <strong>العميل:</strong>
                                <span>${receipt.customer_name}</span>
                            </div>
                            <div class="receipt-row">
                                <strong>المبلغ:</strong>
                                <span>${formatArabicNumber(receipt.amount)}</span>
                            </div>
                            <div class="receipt-row">
                                <strong>تاريخ الدفع:</strong>
                                <span>${receipt.payment_date}</span>
                            </div>
                            <div class="receipt-row">
                                <strong>طريقة الدفع:</strong>
                                <span>${paymentMethodText[receipt.payment_method] || receipt.payment_method}</span>
                            </div>
                            ${receipt.notes ? `
                            <div class="receipt-row">
                                <strong>ملاحظات:</strong>
                                <span>${receipt.notes}</span>
                            </div>
                            ` : ''}
                        </div>

                        <div class="receipt-footer">
                            <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                            <p>نظام محاسبة مبيعات الدراجات النارية</p>
                        </div>
                    </body>
                    </html>
                `);

                printWindow.document.close();
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            } else {
                showAlert('خطأ في جلب بيانات السند', 'error');
            }
        })
        .catch(error => {
            showAlert('خطأ في طباعة السند', 'error');
        });
}

// حذف السند
async function deleteReceipt(receiptId) {
    if (confirm('هل أنت متأكد من حذف هذا السند؟\nسيتم حذفه نهائياً ولا يمكن استرداده.')) {
        try {
            const response = await fetch(`/api/receipts/${receiptId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                showAlert(result.message, 'success');
                location.reload();
            } else {
                showAlert(result.message, 'error');
            }
        } catch (error) {
            showAlert('خطأ في حذف السند', 'error');
        }
    }
}

// إعادة تعيين نموذج السند
function resetReceiptForm() {
    document.querySelector('#addReceiptModal .modal-title').innerHTML = '<i class="fas fa-receipt me-2"></i>إنشاء سند قبض جديد';
    const saveButton = document.querySelector('#addReceiptModal .btn-info');
    saveButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ السند';
    saveButton.onclick = saveReceipt;
    clearForm('addReceiptForm');
    // إعادة تعيين التاريخ الحالي
    document.querySelector('input[name="payment_date"]').value = new Date().toISOString().split('T')[0];
}

// فلترة حسب طريقة الدفع
document.getElementById('paymentMethodFilter').addEventListener('change', function() {
    const selectedMethod = this.value;
    const rows = document.querySelectorAll('#receiptsTable tbody tr');
    
    rows.forEach(row => {
        if (selectedMethod === '') {
            row.style.display = '';
        } else {
            const methodCell = row.cells[4];
            const methodText = methodCell.textContent.toLowerCase();
            
            if (methodText.includes(selectedMethod) || 
                (selectedMethod === 'cash' && methodText.includes('نقداً')) ||
                (selectedMethod === 'bank_transfer' && methodText.includes('تحويل')) ||
                (selectedMethod === 'check' && methodText.includes('شيك'))) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }
    });
});

// إعادة تعيين النموذج عند إغلاق المودال
document.getElementById('addReceiptModal').addEventListener('hidden.bs.modal', function () {
    resetReceiptForm();
});
</script>
{% endblock %}
