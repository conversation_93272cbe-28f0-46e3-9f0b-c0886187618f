{% extends "base.html" %}

{% block title %}إدارة سندات القبض - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-receipt me-2 text-info"></i>
                إدارة سندات القبض
            </h2>
            <button class="btn btn-info btn-lg" data-bs-toggle="modal" data-bs-target="#addReceiptModal">
                <i class="fas fa-plus me-2"></i>
                إنشاء سند قبض جديد
            </button>
        </div>
    </div>
</div>

<!-- شريط البحث والفلاتر -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card filter-section">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="search-box">
                            <input type="text" class="form-control" id="receiptSearch" placeholder="البحث في سندات القبض...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="paymentMethodFilter">
                            <option value="">جميع طرق الدفع</option>
                            <option value="cash">نقداً</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="check">شيك</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="dateFilter">
                    </div>
                    <div class="col-md-3">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-secondary" onclick="exportTableToPDF('receiptsTable', 'قائمة سندات القبض')">
                                <i class="fas fa-file-pdf me-1"></i>
                                PDF
                            </button>
                            <button class="btn btn-outline-secondary" onclick="printTable('receiptsTable')">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول سندات القبض -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="receiptsTable">
                        <thead>
                            <tr>
                                <th>رقم السند</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>تاريخ الدفع</th>
                                <th>طريقة الدفع</th>
                                <th>ملاحظات</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for receipt in receipts %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ receipt.receipt_number }}</strong>
                                </td>
                                <td>{{ receipt.customer_name }}</td>
                                <td>
                                    <strong class="text-success">{{ "{:,.0f}".format(receipt.amount) }} ر.س</strong>
                                </td>
                                <td>{{ receipt.payment_date }}</td>
                                <td>
                                    {% if receipt.payment_method == 'cash' %}
                                        <span class="badge bg-success">نقداً</span>
                                    {% elif receipt.payment_method == 'bank_transfer' %}
                                        <span class="badge bg-primary">تحويل بنكي</span>
                                    {% elif receipt.payment_method == 'check' %}
                                        <span class="badge bg-warning">شيك</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ receipt.payment_method }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ receipt.notes or '-' }}</td>
                                <td>{{ receipt.created_at[:10] }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewReceipt({{ receipt.id }})" 
                                                data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="editReceipt({{ receipt.id }})"
                                                data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="printReceipt({{ receipt.id }})"
                                                data-bs-toggle="tooltip" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteReceipt({{ receipt.id }})"
                                                data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center text-muted py-4">
                                    <i class="fas fa-receipt fa-3x mb-3 d-block"></i>
                                    لا توجد سندات قبض مسجلة حتى الآن
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إنشاء سند قبض جديد -->
<div class="modal fade" id="addReceiptModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-receipt me-2"></i>
                    إنشاء سند قبض جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addReceiptForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">العميل *</label>
                            <select class="form-select" name="customer_id" required>
                                <option value="">اختر العميل</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}">{{ customer.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">المبلغ *</label>
                            <input type="number" class="form-control" name="amount" required step="0.01">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ الدفع *</label>
                            <input type="date" class="form-control" name="payment_date" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">طريقة الدفع *</label>
                            <select class="form-select" name="payment_method" required>
                                <option value="">اختر طريقة الدفع</option>
                                <option value="cash">نقداً</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" onclick="saveReceipt()">
                    <i class="fas fa-save me-2"></i>
                    حفظ السند
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تفعيل البحث في الجدول
searchTable('receiptSearch', 'receiptsTable');

// تعيين التاريخ الحالي
document.querySelector('input[name="payment_date"]').value = new Date().toISOString().split('T')[0];

// حفظ سند قبض جديد
async function saveReceipt() {
    if (!validateForm('addReceiptForm')) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    const form = document.getElementById('addReceiptForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // تحويل المبلغ إلى رقم
    data.amount = parseFloat(data.amount);
    
    const result = await sendData('/api/receipts', data);
    if (result) {
        bootstrap.Modal.getInstance(document.getElementById('addReceiptModal')).hide();
        clearForm('addReceiptForm');
        location.reload();
    }
}

// عرض تفاصيل السند
function viewReceipt(receiptId) {
    showAlert('ميزة عرض التفاصيل قيد التطوير', 'info');
}

// تعديل السند
function editReceipt(receiptId) {
    showAlert('ميزة التعديل قيد التطوير', 'info');
}

// طباعة السند
function printReceipt(receiptId) {
    showAlert('ميزة الطباعة قيد التطوير', 'info');
}

// حذف السند
function deleteReceipt(receiptId) {
    if (confirm('هل أنت متأكد من حذف هذا السند؟')) {
        showAlert('ميزة الحذف قيد التطوير', 'info');
    }
}

// فلترة حسب طريقة الدفع
document.getElementById('paymentMethodFilter').addEventListener('change', function() {
    const selectedMethod = this.value;
    const rows = document.querySelectorAll('#receiptsTable tbody tr');
    
    rows.forEach(row => {
        if (selectedMethod === '') {
            row.style.display = '';
        } else {
            const methodCell = row.cells[4];
            const methodText = methodCell.textContent.toLowerCase();
            
            if (methodText.includes(selectedMethod) || 
                (selectedMethod === 'cash' && methodText.includes('نقداً')) ||
                (selectedMethod === 'bank_transfer' && methodText.includes('تحويل')) ||
                (selectedMethod === 'check' && methodText.includes('شيك'))) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }
    });
});
</script>
{% endblock %}
