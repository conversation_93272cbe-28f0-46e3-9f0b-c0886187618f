<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حقل نوع الدراجة المبسط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .demo-section {
            border: 2px solid #28a745;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .success-highlight {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .feature-box {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="text-center mb-5">
            <h1 class="display-4 text-success">
                <i class="fas fa-motorcycle me-3"></i>
                حقل نوع الدراجة المبسط
            </h1>
            <p class="lead text-muted">سهل الكتابة - بدون تعقيدات</p>
        </div>
        
        <div class="demo-section">
            <h3 class="text-center mb-4">
                <i class="fas fa-edit me-2"></i>
                جرب الحقل الجديد
            </h3>
            
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <label class="form-label fs-5 fw-bold">نوع الدراجة *</label>
                    <input type="text" class="form-control form-control-lg" name="motorcycle_type_text" 
                           placeholder="اكتب نوع الدراجة (مثال: هوندا - CBR 150)" required
                           oninput="showTyping(this.value)">
                    <small class="text-muted">
                        <i class="fas fa-motorcycle me-1"></i>
                        يمكنك كتابة أي نوع دراجة تريده
                    </small>
                </div>
            </div>
            
            <div class="success-highlight mt-4" id="typingResult" style="display: none;">
                <h6><i class="fas fa-check-circle text-success me-2"></i>ما كتبته:</h6>
                <p class="mb-0 fs-5" id="typedText"></p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="feature-box text-center">
                    <i class="fas fa-keyboard fa-3x text-primary mb-3"></i>
                    <h5>سهل الكتابة</h5>
                    <p>حقل نص بسيط بدون تعقيدات</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-box text-center">
                    <i class="fas fa-plus-circle fa-3x text-success mb-3"></i>
                    <h5>أي نوع دراجة</h5>
                    <p>يمكنك كتابة أي نوع تريده</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-box text-center">
                    <i class="fas fa-rocket fa-3x text-warning mb-3"></i>
                    <h5>سريع ومباشر</h5>
                    <p>لا حاجة للبحث في قوائم</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h4><i class="fas fa-lightbulb me-2"></i>أمثلة للكتابة:</h4>
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-group">
                        <li class="list-group-item">
                            <i class="fas fa-motorcycle me-2 text-primary"></i>
                            هوندا - CBR 150
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-motorcycle me-2 text-success"></i>
                            ياماها - YZF-R15
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-motorcycle me-2 text-warning"></i>
                            سوزوكي - GSX-R125
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-group">
                        <li class="list-group-item">
                            <i class="fas fa-motorcycle me-2 text-danger"></i>
                            كاواساكي - Ninja 250
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-motorcycle me-2 text-info"></i>
                            BMW - S1000RR
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-motorcycle me-2 text-dark"></i>
                            دوكاتي - Panigale V4
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-5">
            <a href="http://localhost:5000/invoices" target="_blank" class="btn btn-success btn-lg px-5 py-3">
                <i class="fas fa-external-link-alt me-2"></i>
                جرب في النظام الفعلي
            </a>
        </div>
        
        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-info-circle me-2"></i>كيفية الاستخدام في النظام:</h6>
            <ol>
                <li>اذهب إلى صفحة الفواتير</li>
                <li>اضغط على "إنشاء فاتورة جديدة"</li>
                <li>في حقل نوع الدراجة، اكتب مباشرة</li>
                <li>لا حاجة للبحث في قوائم أو أزرار إضافية</li>
                <li>اكتب أي نوع دراجة تريده</li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showTyping(value) {
            const resultDiv = document.getElementById('typingResult');
            const textSpan = document.getElementById('typedText');
            
            if (value.trim()) {
                textSpan.textContent = value;
                resultDiv.style.display = 'block';
                
                // إضافة تأثير بصري
                textSpan.style.color = '#28a745';
                textSpan.style.fontWeight = 'bold';
            } else {
                resultDiv.style.display = 'none';
            }
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            setTimeout(() => {
                const input = document.querySelector('input[name="motorcycle_type_text"]');
                input.focus();
                
                // تأثير كتابة تلقائي للتوضيح
                const demoText = "هوندا - CBR 150";
                let i = 0;
                
                const typeDemo = setInterval(() => {
                    if (i < demoText.length) {
                        input.value += demoText.charAt(i);
                        showTyping(input.value);
                        i++;
                    } else {
                        clearInterval(typeDemo);
                        setTimeout(() => {
                            input.value = '';
                            showTyping('');
                            input.placeholder = "اكتب نوع الدراجة (مثال: هوندا - CBR 150)";
                        }, 2000);
                    }
                }, 100);
            }, 1000);
        });
    </script>
</body>
</html>
