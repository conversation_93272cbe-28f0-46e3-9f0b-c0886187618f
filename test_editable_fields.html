<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الحقول القابلة للتعديل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>🧪 اختبار الحقول القابلة للتعديل في فواتير المبيعات</h2>
        
        <div class="row mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>نموذج اختبار الحقول الجديدة</h5>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="row g-3">
                                <!-- حقل نوع الدراجة القابل للكتابة -->
                                <div class="col-md-6">
                                    <label class="form-label">نوع الدراجة (قابل للكتابة) *</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="motorcycle_type_text" 
                                               placeholder="اكتب نوع الدراجة..." list="motorcycleList">
                                        <button class="btn btn-outline-secondary" type="button" onclick="toggleMotorcycleSelect()">
                                            <i class="fas fa-list"></i>
                                        </button>
                                    </div>
                                    <datalist id="motorcycleList">
                                        <option value="هوندا - CBR 150" data-id="1" data-price="25000">
                                        <option value="ياماها - YZF-R15" data-id="2" data-price="28000">
                                        <option value="سوزوكي - GSX-R125" data-id="3" data-price="22000">
                                        <option value="كاواساكي - Ninja 250" data-id="4" data-price="35000">
                                    </datalist>
                                    <select class="form-select d-none" name="motorcycle_type_id" onchange="updatePriceFromSelect()">
                                        <option value="">اختر نوع الدراجة</option>
                                        <option value="1" data-price="25000">هوندا - CBR 150</option>
                                        <option value="2" data-price="28000">ياماها - YZF-R15</option>
                                        <option value="3" data-price="22000">سوزوكي - GSX-R125</option>
                                        <option value="4" data-price="35000">كاواساكي - Ninja 250</option>
                                    </select>
                                    <small class="text-muted">يمكنك الكتابة مباشرة أو اختيار من القائمة</small>
                                </div>
                                
                                <!-- حقل المبلغ الإجمالي القابل للكتابة -->
                                <div class="col-md-6">
                                    <label class="form-label">المبلغ الإجمالي (قابل للكتابة) *</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="total_amount" 
                                               placeholder="أدخل المبلغ..." onchange="calculateInstallment()">
                                        <span class="input-group-text">ر.س</span>
                                    </div>
                                    <small class="text-muted">يمكنك كتابة المبلغ مباشرة</small>
                                </div>
                                
                                <!-- باقي الحقول -->
                                <div class="col-md-4">
                                    <label class="form-label">المقدم</label>
                                    <input type="number" class="form-control" name="down_payment" value="0" onchange="calculateInstallment()">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">المبلغ المتبقي</label>
                                    <input type="number" class="form-control" name="remaining_amount" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">عدد الأقساط</label>
                                    <select class="form-select" name="installment_count" onchange="calculateInstallment()">
                                        <option value="">اختر عدد الأقساط</option>
                                        <option value="6">6 أقساط</option>
                                        <option value="12">12 قسط</option>
                                        <option value="18">18 قسط</option>
                                        <option value="24">24 قسط</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">القسط الشهري</label>
                                    <input type="number" class="form-control" name="installment_amount" readonly>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">تاريخ الفاتورة</label>
                                    <input type="date" class="form-control" name="invoice_date">
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <button type="button" class="btn btn-primary" onclick="testSaveInvoice()">
                                    <i class="fas fa-save me-2"></i>اختبار حفظ الفاتورة
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="clearTestForm()">
                                    <i class="fas fa-eraser me-2"></i>مسح النموذج
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>نتائج الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                            <p class="text-muted">املأ النموذج واضغط على "اختبار حفظ الفاتورة"...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاختبار:</h6>
                    <ol>
                        <li><strong>حقل نوع الدراجة:</strong> يمكنك الكتابة مباشرة أو الضغط على زر القائمة للاختيار</li>
                        <li><strong>حقل المبلغ الإجمالي:</strong> يمكنك كتابة أي مبلغ مباشرة</li>
                        <li><strong>الحساب التلقائي:</strong> سيتم حساب المبلغ المتبقي والقسط الشهري تلقائياً</li>
                        <li><strong>الاختبار الفعلي:</strong> اذهب إلى <a href="http://localhost:5000/invoices" target="_blank">صفحة الفواتير</a></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعيين التاريخ الحالي
        document.querySelector('input[name="invoice_date"]').value = new Date().toISOString().split('T')[0];
        
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colorClass = {
                'info': 'text-primary',
                'success': 'text-success',
                'error': 'text-danger',
                'warning': 'text-warning'
            }[type] || 'text-dark';
            
            resultsDiv.innerHTML += `<div class="${colorClass}"><strong>[${timestamp}]</strong> ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // تبديل بين حقل الكتابة والقائمة المنسدلة
        function toggleMotorcycleSelect() {
            const textInput = document.querySelector('input[name="motorcycle_type_text"]');
            const selectInput = document.querySelector('select[name="motorcycle_type_id"]');
            
            if (selectInput.classList.contains('d-none')) {
                selectInput.classList.remove('d-none');
                textInput.parentElement.classList.add('d-none');
                log('تم التبديل إلى القائمة المنسدلة', 'info');
            } else {
                selectInput.classList.add('d-none');
                textInput.parentElement.classList.remove('d-none');
                log('تم التبديل إلى حقل الكتابة', 'info');
            }
        }

        // تحديث السعر من القائمة المنسدلة
        function updatePriceFromSelect() {
            const select = document.querySelector('select[name="motorcycle_type_id"]');
            const priceInput = document.querySelector('input[name="total_amount"]');
            
            if (select.selectedIndex > 0) {
                const price = select.options[select.selectedIndex].dataset.price;
                priceInput.value = price;
                calculateInstallment();
                log(`تم تحديث السعر إلى: ${price} ر.س`, 'success');
            }
        }

        // حساب القسط الشهري
        function calculateInstallment() {
            const totalAmount = parseFloat(document.querySelector('input[name="total_amount"]').value) || 0;
            const downPayment = parseFloat(document.querySelector('input[name="down_payment"]').value) || 0;
            const installmentCount = parseInt(document.querySelector('select[name="installment_count"]').value) || 0;
            
            const remainingAmount = totalAmount - downPayment;
            document.querySelector('input[name="remaining_amount"]').value = remainingAmount;
            
            if (installmentCount > 0) {
                const monthlyInstallment = remainingAmount / installmentCount;
                document.querySelector('input[name="installment_amount"]').value = Math.round(monthlyInstallment);
                log(`تم حساب القسط الشهري: ${Math.round(monthlyInstallment)} ر.س`, 'success');
            }
        }

        // اختبار حفظ الفاتورة
        async function testSaveInvoice() {
            log('🧪 بدء اختبار حفظ الفاتورة...', 'info');
            
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            // معالجة نوع الدراجة
            const motorcycleText = document.querySelector('input[name="motorcycle_type_text"]');
            const motorcycleSelect = document.querySelector('select[name="motorcycle_type_id"]');
            
            if (motorcycleText && !motorcycleText.parentElement.classList.contains('d-none')) {
                data.motorcycle_type_text = motorcycleText.value;
                data.motorcycle_type_id = '';
                log(`نوع الدراجة (نص): ${data.motorcycle_type_text}`, 'info');
            } else if (motorcycleSelect && !motorcycleSelect.classList.contains('d-none')) {
                data.motorcycle_type_id = motorcycleSelect.value;
                data.motorcycle_type_text = motorcycleSelect.options[motorcycleSelect.selectedIndex]?.text || '';
                log(`نوع الدراجة (ID): ${data.motorcycle_type_id}`, 'info');
            }
            
            // إضافة customer_id تجريبي
            data.customer_id = 1;
            
            log(`البيانات المرسلة: ${JSON.stringify(data, null, 2)}`, 'info');
            
            try {
                const response = await fetch('/api/invoices', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                log(`حالة الاستجابة: ${response.status}`, 'info');
                
                if (response.ok) {
                    const result = await response.json();
                    log(`النتيجة: ${JSON.stringify(result, null, 2)}`, 'success');
                    
                    if (result.success) {
                        log('✅ تم حفظ الفاتورة بنجاح!', 'success');
                    } else {
                        log('❌ فشل في حفظ الفاتورة', 'error');
                    }
                } else {
                    log(`❌ خطأ HTTP: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            }
        }

        // مسح النموذج
        function clearTestForm() {
            document.getElementById('testForm').reset();
            document.querySelector('input[name="invoice_date"]').value = new Date().toISOString().split('T')[0];
            
            // إعادة تعيين حقول نوع الدراجة
            const motorcycleText = document.querySelector('input[name="motorcycle_type_text"]');
            const motorcycleSelect = document.querySelector('select[name="motorcycle_type_id"]');
            
            motorcycleText.parentElement.classList.remove('d-none');
            motorcycleSelect.classList.add('d-none');
            
            log('تم مسح النموذج', 'info');
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            log('🚀 مرحباً بك في اختبار الحقول القابلة للتعديل!', 'info');
            log('💡 جرب كتابة نوع دراجة جديد في الحقل الأول', 'info');
            log('💡 جرب كتابة مبلغ مخصص في حقل المبلغ الإجمالي', 'info');
        });
    </script>
</body>
</html>
