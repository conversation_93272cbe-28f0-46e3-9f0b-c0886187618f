
/* ملف CSS للطباعة */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
    font-family: 'Cairo', Arial, sans-serif !important;
    direction: rtl !important;
    font-size: 14px;
    line-height: 1.6;
    margin: 20px;
}

.print-header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #333;
    padding-bottom: 20px;
}

.print-header h1 {
    color: #2c3e50;
    margin: 0;
    font-size: 24px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    font-size: 12px;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: right;
    vertical-align: middle;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #2c3e50;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

@media print {
    body { margin: 0; }
    .no-print { display: none; }
    table { page-break-inside: auto; }
    tr { page-break-inside: avoid; page-break-after: auto; }
    thead { display: table-header-group; }
}
