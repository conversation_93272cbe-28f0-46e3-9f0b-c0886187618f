{% extends "base.html" %}

{% block title %}إدارة الفواتير - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-file-invoice me-2 text-warning"></i>
                إدارة الفواتير
            </h2>
            <button class="btn btn-warning btn-lg" onclick="openAddInvoiceModal()">
                <i class="fas fa-plus me-2"></i>
                إنشاء فاتورة جديدة
            </button>
        </div>
    </div>
</div>

<!-- شريط البحث والفلاتر -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card filter-section">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="search-box">
                            <input type="text" class="form-control" id="invoiceSearch" placeholder="البحث في الفواتير...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشطة</option>
                            <option value="completed">مكتملة</option>
                            <option value="cancelled">ملغية</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="dateFilter">
                    </div>
                    <div class="col-md-3">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-secondary" onclick="exportTableToPDF('invoicesTable', 'قائمة الفواتير')">
                                <i class="fas fa-file-pdf me-1"></i>
                                PDF
                            </button>
                            <button class="btn btn-outline-secondary" onclick="printTable('invoicesTable')">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الفواتير -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="invoicesTable">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>نوع الدراجة</th>
                                <th>المبلغ الإجمالي</th>
                                <th>المقدم</th>
                                <th>المتبقي</th>
                                <th>قسط شهري</th>
                                <th>عدد الأقساط</th>
                                <th>تاريخ الفاتورة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ invoice.invoice_number }}</strong>
                                </td>
                                <td>{{ invoice.customer_name }}</td>
                                <td>
                                    {{ invoice.motorcycle_name }}
                                    <small class="text-muted d-block">{{ invoice.model }}</small>
                                </td>
                                <td>
                                    <strong class="text-success">{{ invoice.total_amount|format_currency }}</strong>
                                </td>
                                <td>{{ invoice.down_payment|format_currency }}</td>
                                <td>
                                    <strong class="text-danger">{{ invoice.remaining_amount|format_currency }}</strong>
                                </td>
                                <td>{{ invoice.installment_amount|format_currency }}</td>
                                <td>
                                    <span class="badge bg-info">{{ invoice.installment_count }} قسط</span>
                                </td>
                                <td>{{ invoice.invoice_date }}</td>
                                <td>
                                    {% if invoice.status == 'active' %}
                                        <span class="status-badge status-active">نشطة</span>
                                    {% elif invoice.status == 'completed' %}
                                        <span class="status-badge status-paid">مكتملة</span>
                                    {% else %}
                                        <span class="status-badge status-overdue">ملغية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewInvoice({{ invoice.id }})" 
                                                data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="viewInstallments({{ invoice.id }})"
                                                data-bs-toggle="tooltip" title="عرض الأقساط">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="editInvoice({{ invoice.id }})"
                                                data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="printInvoice({{ invoice.id }})"
                                                data-bs-toggle="tooltip" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="11" class="text-center text-muted py-4">
                                    <i class="fas fa-file-invoice fa-3x mb-3 d-block"></i>
                                    لا توجد فواتير مسجلة حتى الآن
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إنشاء فاتورة جديدة -->
<div class="modal fade" id="addInvoiceModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-invoice me-2"></i>
                    إنشاء فاتورة جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addInvoiceForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">العميل *</label>
                            <select class="form-select" name="customer_id" required>
                                <option value="">اختر العميل</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}">{{ customer.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">نوع الدراجة *</label>
                            <input type="text" class="form-control" name="motorcycle_type_text"
                                   placeholder="اكتب نوع الدراجة (مثال: هوندا - CBR 150)" required>
                            <small class="text-muted">
                                <i class="fas fa-motorcycle me-1"></i>
                                يمكنك كتابة أي نوع دراجة تريده
                            </small>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المبلغ الإجمالي *</label>
                            <div class="input-group">
                                <input type="number" class="form-control" name="total_amount" required
                                       placeholder="أدخل المبلغ..." onchange="calculateInstallment()">
                                <span class="input-group-text">ر.س</span>
                            </div>
                            <small class="text-muted">يمكنك كتابة المبلغ مباشرة</small>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المقدم</label>
                            <input type="number" class="form-control" name="down_payment" value="0" onchange="calculateInstallment()">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المبلغ المتبقي</label>
                            <input type="number" class="form-control" name="remaining_amount" readonly>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">عدد الأقساط *</label>
                            <select class="form-select" name="installment_count" required onchange="calculateInstallment()">
                                <option value="">اختر عدد الأقساط</option>
                                <option value="6">6 أقساط</option>
                                <option value="12">12 قسط</option>
                                <option value="18">18 قسط</option>
                                <option value="24">24 قسط</option>
                                <option value="36">36 قسط</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">القسط الشهري</label>
                            <input type="number" class="form-control" name="installment_amount" readonly>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">تاريخ الفاتورة *</label>
                            <input type="date" class="form-control" name="invoice_date" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="saveInvoice()">
                    <i class="fas fa-save me-2"></i>
                    حفظ الفاتورة
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تفعيل البحث في الجدول
searchTable('invoiceSearch', 'invoicesTable');

// تعيين التاريخ الحالي
document.querySelector('input[name="invoice_date"]').value = new Date().toISOString().split('T')[0];

// تحديث السعر (وظيفة مبسطة للتوافق مع الكود القديم)
function updatePrice() {
    // وظيفة فارغة للتوافق مع الكود القديم
    console.log('تم استدعاء updatePrice - لا حاجة لتحديث السعر تلقائياً');
}

// حساب القسط الشهري
function calculateInstallment() {
    const totalAmount = parseFloat(document.querySelector('input[name="total_amount"]').value) || 0;
    const downPayment = parseFloat(document.querySelector('input[name="down_payment"]').value) || 0;
    const installmentCount = parseInt(document.querySelector('select[name="installment_count"]').value) || 0;
    
    const remainingAmount = totalAmount - downPayment;
    document.querySelector('input[name="remaining_amount"]').value = remainingAmount;
    
    if (installmentCount > 0) {
        const monthlyInstallment = remainingAmount / installmentCount;
        document.querySelector('input[name="installment_amount"]').value = Math.round(monthlyInstallment);
    }
}

// حفظ فاتورة جديدة
async function saveInvoice() {
    if (!validateForm('addInvoiceForm')) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    const form = document.getElementById('addInvoiceForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    // معالجة نوع الدراجة (حقل كتابة بسيط)
    const motorcycleText = document.querySelector('input[name="motorcycle_type_text"]');

    if (!motorcycleText || !motorcycleText.value.trim()) {
        showAlert('يرجى كتابة نوع الدراجة', 'warning');
        return;
    }

    data.motorcycle_type_text = motorcycleText.value.trim();
    data.motorcycle_type_id = ''; // لا يوجد ID محدد

    // التحقق من وجود المبلغ الإجمالي
    if (!data.total_amount || parseFloat(data.total_amount) <= 0) {
        showAlert('يرجى إدخال المبلغ الإجمالي', 'warning');
        return;
    }

    // تحويل القيم الرقمية
    data.total_amount = parseFloat(data.total_amount);
    data.down_payment = parseFloat(data.down_payment) || 0;
    data.installment_amount = parseFloat(data.installment_amount) || 0;
    data.installment_count = parseInt(data.installment_count) || 0;

    console.log('بيانات الفاتورة للحفظ:', data);

    const result = await sendData('/api/invoices', data);
    if (result) {
        bootstrap.Modal.getInstance(document.getElementById('addInvoiceModal')).hide();
        clearForm('addInvoiceForm');
        location.reload();
    }
}

// عرض تفاصيل الفاتورة
async function viewInvoice(invoiceId) {
    console.log('عرض تفاصيل الفاتورة رقم:', invoiceId);

    try {
        showLoading();

        const response = await fetch(`/api/invoices/${invoiceId}`);
        console.log('استجابة الخادم:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('بيانات الفاتورة:', data);

        if (data.success) {
            const invoice = data.invoice;

            const statusText = {
                'active': 'نشطة',
                'completed': 'مكتملة',
                'cancelled': 'ملغية'
            };

            const details = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">بيانات الفاتورة</h6>
                        <p><strong>رقم الفاتورة:</strong> ${invoice.invoice_number}</p>
                        <p><strong>تاريخ الفاتورة:</strong> ${invoice.invoice_date}</p>
                        <p><strong>الحالة:</strong> <span class="badge bg-info">${statusText[invoice.status] || invoice.status}</span></p>
                        <p><strong>تاريخ الإنشاء:</strong> ${invoice.created_at}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">بيانات العميل</h6>
                        <p><strong>اسم العميل:</strong> ${invoice.customer_name}</p>
                        <p><strong>رقم الهاتف:</strong> ${invoice.customer_phone || 'غير محدد'}</p>
                        <p><strong>الضامن:</strong> ${invoice.guarantor_name || 'بدون ضامن'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">بيانات الدراجة</h6>
                        <p><strong>نوع الدراجة:</strong> ${invoice.motorcycle_name}</p>
                        <p><strong>الموديل:</strong> ${invoice.motorcycle_model}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-danger">المبالغ المالية</h6>
                        <p><strong>المبلغ الإجمالي:</strong> ${formatArabicNumber(invoice.total_amount)}</p>
                        <p><strong>المقدم:</strong> ${formatArabicNumber(invoice.down_payment)}</p>
                        <p><strong>المتبقي:</strong> ${formatArabicNumber(invoice.remaining_amount)}</p>
                        <p><strong>القسط الشهري:</strong> ${formatArabicNumber(invoice.installment_amount)}</p>
                        <p><strong>عدد الأقساط:</strong> ${invoice.installment_count} قسط</p>
                    </div>
                </div>
            `;

            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>
                                تفاصيل الفاتورة ${invoice.invoice_number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">${details}</div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-success" onclick="viewInstallments(${invoiceId})">
                                <i class="fas fa-list me-2"></i>عرض الأقساط
                            </button>
                            <button type="button" class="btn btn-info" onclick="printInvoice(${invoiceId})">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-warning" onclick="editInvoice(${invoiceId}); bootstrap.Modal.getInstance(this.closest('.modal')).hide();">
                                <i class="fas fa-edit me-2"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });

            hideLoading();

        } else {
            hideLoading();
            showAlert(data.message || 'خطأ في جلب بيانات الفاتورة', 'error');
        }
    } catch (error) {
        hideLoading();
        console.error('خطأ في عرض تفاصيل الفاتورة:', error);
        showAlert('خطأ في جلب بيانات الفاتورة: ' + error.message, 'error');
    }
}

// عرض الأقساط
async function viewInstallments(invoiceId) {
    console.log('عرض أقساط الفاتورة رقم:', invoiceId);

    try {
        showLoading();

        const response = await fetch(`/api/invoices/${invoiceId}/installments`);
        console.log('استجابة الأقساط:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('بيانات الأقساط:', data);

        if (data.success) {
            const installments = data.installments;

            let installmentsTable = '';
            if (installments.length > 0) {
                installmentsTable = `
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم القسط</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>تاريخ الدفع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                installments.forEach(installment => {
                    const statusBadge = {
                        'pending': '<span class="badge bg-warning">مستحق</span>',
                        'paid': '<span class="badge bg-success">مدفوع</span>',
                        'overdue': '<span class="badge bg-danger">متأخر</span>'
                    };

                    installmentsTable += `
                        <tr>
                            <td>${installment.installment_number}</td>
                            <td>${formatArabicNumber(installment.amount)}</td>
                            <td>${installment.due_date}</td>
                            <td>${installment.payment_date || 'لم يُدفع'}</td>
                            <td>${statusBadge[installment.status] || installment.status}</td>
                            <td>
                                ${installment.status === 'pending' ?
                                    `<button class="btn btn-sm btn-success" onclick="markInstallmentAsPaid(${installment.id})">
                                        <i class="fas fa-check me-1"></i>تسديد
                                    </button>` :
                                    '<span class="text-muted">-</span>'
                                }
                            </td>
                        </tr>
                    `;
                });

                installmentsTable += `
                            </tbody>
                        </table>
                    </div>
                `;
            } else {
                installmentsTable = `
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle me-2"></i>
                        لم يتم إنشاء أقساط لهذه الفاتورة بعد
                    </div>
                `;
            }

            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-list me-2"></i>
                                أقساط الفاتورة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${installmentsTable}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" onclick="generateInstallments(${invoiceId})">
                                <i class="fas fa-plus me-2"></i>إنشاء الأقساط
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });

            hideLoading();

        } else {
            hideLoading();
            showAlert(data.message || 'خطأ في جلب بيانات الأقساط', 'error');
        }
    } catch (error) {
        hideLoading();
        console.error('خطأ في عرض الأقساط:', error);
        showAlert('خطأ في جلب بيانات الأقساط: ' + error.message, 'error');
    }
}

// تعديل الفاتورة
async function editInvoice(invoiceId) {
    console.log('تعديل الفاتورة رقم:', invoiceId);

    try {
        showLoading();

        const response = await fetch(`/api/invoices/${invoiceId}`);
        console.log('استجابة الخادم:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('بيانات الفاتورة للتعديل:', data);

        if (data.success) {
            const invoice = data.invoice;

            // ملء النموذج ببيانات الفاتورة
            const form = document.getElementById('addInvoiceForm');
            if (!form) {
                throw new Error('النموذج غير موجود');
            }

            const customerSelect = form.querySelector('select[name="customer_id"]');
            const motorcycleTextInput = form.querySelector('input[name="motorcycle_type_text"]');
            const totalAmountInput = form.querySelector('input[name="total_amount"]');
            const downPaymentInput = form.querySelector('input[name="down_payment"]');
            const remainingAmountInput = form.querySelector('input[name="remaining_amount"]');
            const installmentCountSelect = form.querySelector('select[name="installment_count"]');
            const installmentAmountInput = form.querySelector('input[name="installment_amount"]');
            const invoiceDateInput = form.querySelector('input[name="invoice_date"]');

            if (customerSelect) customerSelect.value = invoice.customer_id || '';

            // ملء بيانات نوع الدراجة في حقل الكتابة البسيط
            if (motorcycleTextInput) {
                const motorcycleName = invoice.motorcycle_name || '';
                const motorcycleModel = invoice.motorcycle_model || '';
                const fullName = motorcycleName + (motorcycleModel ? ' - ' + motorcycleModel : '');
                motorcycleTextInput.value = fullName;
            }

            if (totalAmountInput) totalAmountInput.value = invoice.total_amount || '';
            if (downPaymentInput) downPaymentInput.value = invoice.down_payment || '';
            if (remainingAmountInput) remainingAmountInput.value = invoice.remaining_amount || '';
            if (installmentCountSelect) installmentCountSelect.value = invoice.installment_count || '';
            if (installmentAmountInput) installmentAmountInput.value = invoice.installment_amount || '';
            if (invoiceDateInput) invoiceDateInput.value = invoice.invoice_date || '';

            // تغيير عنوان المودال
            const modalTitle = document.querySelector('#addInvoiceModal .modal-title');
            if (modalTitle) {
                modalTitle.innerHTML = '<i class="fas fa-edit me-2"></i>تعديل الفاتورة ' + invoice.invoice_number;
            }

            // تغيير نص الزر وإضافة معرف الفاتورة
            const saveButton = document.querySelector('#addInvoiceModal .btn-warning');
            if (saveButton) {
                saveButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ التعديلات';
                saveButton.setAttribute('data-invoice-id', invoiceId);
                saveButton.onclick = () => updateInvoice(invoiceId);
            }

            // إظهار المودال
            const modalElement = document.getElementById('addInvoiceModal');
            if (modalElement) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            }

            hideLoading();
            showAlert('تم تحميل بيانات الفاتورة للتعديل', 'info');

        } else {
            hideLoading();
            showAlert(data.message || 'خطأ في جلب بيانات الفاتورة', 'error');
        }
    } catch (error) {
        hideLoading();
        console.error('خطأ في تعديل الفاتورة:', error);
        showAlert('خطأ في جلب بيانات الفاتورة: ' + error.message, 'error');
    }
}

// تحديث بيانات الفاتورة
async function updateInvoice(invoiceId) {
    console.log('تحديث الفاتورة رقم:', invoiceId);

    if (!validateForm('addInvoiceForm')) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    const form = document.getElementById('addInvoiceForm');
    if (!form) {
        showAlert('النموذج غير موجود', 'error');
        return;
    }

    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    // معالجة نوع الدراجة (حقل كتابة بسيط)
    const motorcycleText = document.querySelector('input[name="motorcycle_type_text"]');

    if (!motorcycleText || !motorcycleText.value.trim()) {
        showAlert('يرجى كتابة نوع الدراجة', 'warning');
        return;
    }

    data.motorcycle_type_text = motorcycleText.value.trim();
    data.motorcycle_type_id = ''; // لا يوجد ID محدد

    console.log('🏍️ نوع الدراجة الجديد:', data.motorcycle_type_text);

    // التحقق من وجود المبلغ الإجمالي
    if (!data.total_amount || parseFloat(data.total_amount) <= 0) {
        showAlert('يرجى إدخال المبلغ الإجمالي', 'warning');
        return;
    }

    // تحويل القيم الرقمية
    data.total_amount = parseFloat(data.total_amount);
    data.down_payment = parseFloat(data.down_payment) || 0;
    data.installment_amount = parseFloat(data.installment_amount) || 0;
    data.installment_count = parseInt(data.installment_count) || 0;
    data.remaining_amount = data.total_amount - data.down_payment;

    console.log('📊 بيانات التحديث الكاملة:', data);

    try {
        showLoading();

        const response = await fetch(`/api/invoices/${invoiceId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        console.log('استجابة التحديث:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('نتيجة التحديث:', result);

        hideLoading();

        if (result.success) {
            showAlert(result.message || 'تم تحديث الفاتورة بنجاح', 'success');
            console.log('✅ تم تحديث الفاتورة بنجاح');

            // إغلاق المودال
            const modalInstance = bootstrap.Modal.getInstance(document.getElementById('addInvoiceModal'));
            if (modalInstance) {
                modalInstance.hide();
            }

            // إعادة تعيين النموذج
            resetInvoiceForm();

            // إعادة تحميل الصفحة لإظهار التحديثات
            console.log('🔄 إعادة تحميل الصفحة خلال 2 ثانية...');
            setTimeout(() => {
                console.log('🔄 إعادة تحميل الصفحة الآن...');
                location.reload();
            }, 2000);

        } else {
            showAlert(result.message || 'خطأ في تحديث الفاتورة', 'error');
            console.error('❌ فشل تحديث الفاتورة:', result.message);
        }
    } catch (error) {
        hideLoading();
        console.error('خطأ في تحديث الفاتورة:', error);
        showAlert('خطأ في تحديث الفاتورة: ' + error.message, 'error');
    }
}

// طباعة الفاتورة
function printInvoice(invoiceId) {
    fetch(`/api/invoices/${invoiceId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const invoice = data.invoice;

                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <!DOCTYPE html>
                    <html dir="rtl" lang="ar">
                    <head>
                        <meta charset="UTF-8">
                        <title>فاتورة مبيعات - ${invoice.invoice_number}</title>
                        <style>
                            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
                            body {
                                font-family: 'Cairo', Arial, sans-serif;
                                direction: rtl;
                                margin: 20px;
                                font-size: 16px;
                            }
                            .invoice-header {
                                text-align: center;
                                border-bottom: 2px solid #333;
                                padding-bottom: 20px;
                                margin-bottom: 30px;
                            }
                            .invoice-body {
                                margin: 30px 0;
                            }
                            .invoice-row {
                                display: flex;
                                justify-content: space-between;
                                margin: 15px 0;
                                padding: 10px;
                                border-bottom: 1px solid #eee;
                            }
                            .invoice-footer {
                                margin-top: 50px;
                                text-align: center;
                                border-top: 1px solid #333;
                                padding-top: 20px;
                            }
                            @media print { body { margin: 0; } }
                        </style>
                    </head>
                    <body>
                        <div class="invoice-header">
                            <h1>فاتورة مبيعات</h1>
                            <h2>رقم الفاتورة: ${invoice.invoice_number}</h2>
                            <p>تاريخ الفاتورة: ${invoice.invoice_date}</p>
                        </div>

                        <div class="invoice-body">
                            <div class="invoice-row">
                                <strong>العميل:</strong>
                                <span>${invoice.customer_name}</span>
                            </div>
                            <div class="invoice-row">
                                <strong>نوع الدراجة:</strong>
                                <span>${invoice.motorcycle_name} - ${invoice.motorcycle_model}</span>
                            </div>
                            <div class="invoice-row">
                                <strong>المبلغ الإجمالي:</strong>
                                <span>${formatArabicNumber(invoice.total_amount)}</span>
                            </div>
                            <div class="invoice-row">
                                <strong>المقدم:</strong>
                                <span>${formatArabicNumber(invoice.down_payment)}</span>
                            </div>
                            <div class="invoice-row">
                                <strong>المبلغ المتبقي:</strong>
                                <span>${formatArabicNumber(invoice.remaining_amount)}</span>
                            </div>
                            <div class="invoice-row">
                                <strong>القسط الشهري:</strong>
                                <span>${formatArabicNumber(invoice.installment_amount)}</span>
                            </div>
                            <div class="invoice-row">
                                <strong>عدد الأقساط:</strong>
                                <span>${invoice.installment_count} قسط</span>
                            </div>
                        </div>

                        <div class="invoice-footer">
                            <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                            <p>نظام محاسبة مبيعات الدراجات النارية</p>
                        </div>
                    </body>
                    </html>
                `);

                printWindow.document.close();
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            } else {
                showAlert('خطأ في جلب بيانات الفاتورة', 'error');
            }
        })
        .catch(error => {
            showAlert('خطأ في طباعة الفاتورة', 'error');
        });
}

// فتح مودال إنشاء فاتورة جديدة
function openAddInvoiceModal() {
    console.log('فتح مودال إنشاء فاتورة جديدة');

    // التأكد من إعادة تعيين النموذج
    resetInvoiceForm();

    // فتح المودال
    const modalElement = document.getElementById('addInvoiceModal');
    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    } else {
        console.error('مودال الفاتورة غير موجود');
        showAlert('خطأ في فتح نافذة إنشاء الفاتورة', 'error');
    }
}

// إعادة تعيين نموذج الفاتورة
function resetInvoiceForm() {
    console.log('إعادة تعيين نموذج الفاتورة');

    // إعادة تعيين عنوان المودال
    const modalTitle = document.querySelector('#addInvoiceModal .modal-title');
    if (modalTitle) {
        modalTitle.innerHTML = '<i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة جديدة';
    }

    // إعادة تعيين زر الحفظ
    const saveButton = document.querySelector('#addInvoiceModal .btn-warning');
    if (saveButton) {
        saveButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ الفاتورة';
        saveButton.onclick = saveInvoice;
        saveButton.removeAttribute('data-invoice-id');
    }

    // إعادة تعيين حقل نوع الدراجة (حقل كتابة بسيط)
    const motorcycleTextInput = document.querySelector('input[name="motorcycle_type_text"]');
    if (motorcycleTextInput) {
        motorcycleTextInput.value = '';
    }

    // مسح النموذج
    clearForm('addInvoiceForm');

    // إعادة تعيين التاريخ الحالي
    const dateInput = document.querySelector('input[name="invoice_date"]');
    if (dateInput) {
        dateInput.value = new Date().toISOString().split('T')[0];
    }

    // إزالة أي رسائل خطأ
    const form = document.getElementById('addInvoiceForm');
    if (form) {
        const invalidInputs = form.querySelectorAll('.is-invalid');
        invalidInputs.forEach(input => {
            input.classList.remove('is-invalid');
        });
    }
}

// وظائف إضافية للأقساط
function markInstallmentAsPaid(installmentId) {
    if (confirm('هل أنت متأكد من تسديد هذا القسط؟')) {
        showAlert('ميزة تسديد الأقساط قيد التطوير', 'info');
    }
}

function generateInstallments(invoiceId) {
    if (confirm('هل تريد إنشاء جدول الأقساط لهذه الفاتورة؟')) {
        showAlert('ميزة إنشاء الأقساط قيد التطوير', 'info');
    }
}

// إعادة تعيين النموذج عند إغلاق المودال
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('addInvoiceModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function () {
            resetInvoiceForm();
        });
    }
});
</script>
{% endblock %}
