{% extends "base.html" %}

{% block title %}إدارة الفواتير - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-file-invoice me-2 text-warning"></i>
                إدارة الفواتير
            </h2>
            <button class="btn btn-warning btn-lg" data-bs-toggle="modal" data-bs-target="#addInvoiceModal">
                <i class="fas fa-plus me-2"></i>
                إنشاء فاتورة جديدة
            </button>
        </div>
    </div>
</div>

<!-- شريط البحث والفلاتر -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card filter-section">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="search-box">
                            <input type="text" class="form-control" id="invoiceSearch" placeholder="البحث في الفواتير...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشطة</option>
                            <option value="completed">مكتملة</option>
                            <option value="cancelled">ملغية</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="dateFilter">
                    </div>
                    <div class="col-md-3">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-secondary" onclick="exportTableToPDF('invoicesTable', 'قائمة الفواتير')">
                                <i class="fas fa-file-pdf me-1"></i>
                                PDF
                            </button>
                            <button class="btn btn-outline-secondary" onclick="printTable('invoicesTable')">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الفواتير -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="invoicesTable">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>نوع الدراجة</th>
                                <th>المبلغ الإجمالي</th>
                                <th>المقدم</th>
                                <th>المتبقي</th>
                                <th>قسط شهري</th>
                                <th>عدد الأقساط</th>
                                <th>تاريخ الفاتورة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ invoice.invoice_number }}</strong>
                                </td>
                                <td>{{ invoice.customer_name }}</td>
                                <td>
                                    {{ invoice.motorcycle_name }}
                                    <small class="text-muted d-block">{{ invoice.model }}</small>
                                </td>
                                <td>
                                    <strong class="text-success">{{ invoice.total_amount|format_currency }}</strong>
                                </td>
                                <td>{{ invoice.down_payment|format_currency }}</td>
                                <td>
                                    <strong class="text-danger">{{ invoice.remaining_amount|format_currency }}</strong>
                                </td>
                                <td>{{ invoice.installment_amount|format_currency }}</td>
                                <td>
                                    <span class="badge bg-info">{{ invoice.installment_count }} قسط</span>
                                </td>
                                <td>{{ invoice.invoice_date }}</td>
                                <td>
                                    {% if invoice.status == 'active' %}
                                        <span class="status-badge status-active">نشطة</span>
                                    {% elif invoice.status == 'completed' %}
                                        <span class="status-badge status-paid">مكتملة</span>
                                    {% else %}
                                        <span class="status-badge status-overdue">ملغية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewInvoice({{ invoice.id }})" 
                                                data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="viewInstallments({{ invoice.id }})"
                                                data-bs-toggle="tooltip" title="عرض الأقساط">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="editInvoice({{ invoice.id }})"
                                                data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="printInvoice({{ invoice.id }})"
                                                data-bs-toggle="tooltip" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="11" class="text-center text-muted py-4">
                                    <i class="fas fa-file-invoice fa-3x mb-3 d-block"></i>
                                    لا توجد فواتير مسجلة حتى الآن
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إنشاء فاتورة جديدة -->
<div class="modal fade" id="addInvoiceModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-invoice me-2"></i>
                    إنشاء فاتورة جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addInvoiceForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">العميل *</label>
                            <select class="form-select" name="customer_id" required>
                                <option value="">اختر العميل</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}">{{ customer.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">نوع الدراجة *</label>
                            <select class="form-select" name="motorcycle_type_id" required onchange="updatePrice()">
                                <option value="">اختر نوع الدراجة</option>
                                {% for motorcycle in motorcycles %}
                                <option value="{{ motorcycle.id }}" data-price="{{ motorcycle.price }}">
                                    {{ motorcycle.name }} - {{ motorcycle.model }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المبلغ الإجمالي *</label>
                            <input type="number" class="form-control" name="total_amount" required readonly>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المقدم</label>
                            <input type="number" class="form-control" name="down_payment" value="0" onchange="calculateInstallment()">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المبلغ المتبقي</label>
                            <input type="number" class="form-control" name="remaining_amount" readonly>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">عدد الأقساط *</label>
                            <select class="form-select" name="installment_count" required onchange="calculateInstallment()">
                                <option value="">اختر عدد الأقساط</option>
                                <option value="6">6 أقساط</option>
                                <option value="12">12 قسط</option>
                                <option value="18">18 قسط</option>
                                <option value="24">24 قسط</option>
                                <option value="36">36 قسط</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">القسط الشهري</label>
                            <input type="number" class="form-control" name="installment_amount" readonly>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">تاريخ الفاتورة *</label>
                            <input type="date" class="form-control" name="invoice_date" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="saveInvoice()">
                    <i class="fas fa-save me-2"></i>
                    حفظ الفاتورة
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تفعيل البحث في الجدول
searchTable('invoiceSearch', 'invoicesTable');

// تعيين التاريخ الحالي
document.querySelector('input[name="invoice_date"]').value = new Date().toISOString().split('T')[0];

// تحديث السعر عند اختيار نوع الدراجة
function updatePrice() {
    const select = document.querySelector('select[name="motorcycle_type_id"]');
    const priceInput = document.querySelector('input[name="total_amount"]');
    
    if (select.selectedIndex > 0) {
        const price = select.options[select.selectedIndex].dataset.price;
        priceInput.value = price;
        calculateInstallment();
    } else {
        priceInput.value = '';
    }
}

// حساب القسط الشهري
function calculateInstallment() {
    const totalAmount = parseFloat(document.querySelector('input[name="total_amount"]').value) || 0;
    const downPayment = parseFloat(document.querySelector('input[name="down_payment"]').value) || 0;
    const installmentCount = parseInt(document.querySelector('select[name="installment_count"]').value) || 0;
    
    const remainingAmount = totalAmount - downPayment;
    document.querySelector('input[name="remaining_amount"]').value = remainingAmount;
    
    if (installmentCount > 0) {
        const monthlyInstallment = remainingAmount / installmentCount;
        document.querySelector('input[name="installment_amount"]').value = Math.round(monthlyInstallment);
    }
}

// حفظ فاتورة جديدة
async function saveInvoice() {
    if (!validateForm('addInvoiceForm')) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    const form = document.getElementById('addInvoiceForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // تحويل القيم الرقمية
    data.total_amount = parseFloat(data.total_amount);
    data.down_payment = parseFloat(data.down_payment);
    data.installment_amount = parseFloat(data.installment_amount);
    data.installment_count = parseInt(data.installment_count);
    
    const result = await sendData('/api/invoices', data);
    if (result) {
        bootstrap.Modal.getInstance(document.getElementById('addInvoiceModal')).hide();
        clearForm('addInvoiceForm');
        location.reload();
    }
}

// عرض تفاصيل الفاتورة
function viewInvoice(invoiceId) {
    showAlert('ميزة عرض التفاصيل قيد التطوير', 'info');
}

// عرض الأقساط
function viewInstallments(invoiceId) {
    showAlert('ميزة عرض الأقساط قيد التطوير', 'info');
}

// تعديل الفاتورة
function editInvoice(invoiceId) {
    showAlert('ميزة التعديل قيد التطوير', 'info');
}

// طباعة الفاتورة
function printInvoice(invoiceId) {
    showAlert('ميزة الطباعة قيد التطوير', 'info');
}
</script>
{% endblock %}
