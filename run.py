#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app
    print("تم تحميل التطبيق بنجاح")
    print("يمكنك الوصول للنظام عبر: http://localhost:5000")
    print("للإيقاف اضغط Ctrl+C")
    app.run(debug=True, host='0.0.0.0', port=5000)
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    print("يرجى تثبيت المتطلبات باستخدام: pip install -r requirements.txt")
except Exception as e:
    print(f"خطأ في تشغيل التطبيق: {e}")
    import traceback
    traceback.print_exc()
