#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إنشاء أيقونات بسيطة للتطبيق
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_icon(size, filename):
    """إنشاء أيقونة بحجم محدد"""
    # إنشاء صورة جديدة بخلفية زرقاء
    img = Image.new('RGB', (size, size), color='#2c3e50')
    draw = ImageDraw.Draw(img)
    
    # رسم دائرة بيضاء في المنتصف
    margin = size // 8
    draw.ellipse([margin, margin, size-margin, size-margin], fill='white')
    
    # إضافة نص "د" في المنتصف
    try:
        # محاولة استخدام خط عربي
        font_size = size // 3
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # استخدام الخط الافتراضي
        font = ImageFont.load_default()
    
    # رسم النص
    text = "د"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    draw.text((x, y), text, fill='#2c3e50', font=font)
    
    # حفظ الصورة
    img.save(filename, 'PNG')
    print(f"تم إنشاء الأيقونة: {filename}")

def main():
    """إنشاء جميع الأيقونات المطلوبة"""
    # إنشاء مجلد الصور إذا لم يكن موجوداً
    images_dir = 'static/images'
    if not os.path.exists(images_dir):
        os.makedirs(images_dir)
    
    # أحجام الأيقونات المطلوبة
    sizes = [72, 96, 128, 144, 152, 192, 384, 512]
    
    for size in sizes:
        filename = f"{images_dir}/icon-{size}.png"
        create_icon(size, filename)
    
    print("\nتم إنشاء جميع الأيقونات بنجاح!")
    print("يمكنك الآن استخدام التطبيق كـ PWA على الهواتف الذكية.")

if __name__ == "__main__":
    try:
        main()
    except ImportError:
        print("تحتاج إلى تثبيت مكتبة Pillow لإنشاء الأيقونات:")
        print("pip install Pillow")
    except Exception as e:
        print(f"حدث خطأ: {e}")
        print("سيتم تشغيل النظام بدون أيقونات مخصصة.")
