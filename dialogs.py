#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نوافذ الحوار للتطبيق
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter.font import Font
import sqlite3
from datetime import datetime, date

class CustomerDialog:
    def __init__(self, parent, title, customer_data=None):
        self.result = None
        self.parent = parent

        # ألوان التصميم
        self.colors = {
            'primary': '#2c3e50',
            'secondary': '#3498db',
            'success': '#27ae60',
            'white': '#ffffff',
            'light': '#ecf0f1'
        }

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"👤 {title}")
        self.dialog.geometry("600x500")
        self.dialog.configure(bg=self.colors['light'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # تعيين الخط
        self.arabic_font = Font(family="Segoe UI", size=11)
        self.title_font = Font(family="Segoe UI", size=16, weight="bold")
        
        self.create_widgets(customer_data)
        
        # توسيط النافذة
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def create_widgets(self, customer_data):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي مع خلفية جميلة
        main_frame = tk.Frame(self.dialog, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # الهيدر
        header_frame = tk.Frame(main_frame, bg=self.colors['primary'], relief='solid', bd=1)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        header_label = tk.Label(
            header_frame,
            text="👤 بيانات العميل",
            font=self.title_font,
            bg=self.colors['primary'],
            fg='white'
        )
        header_label.pack(pady=15)

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg=self.colors['white'], relief='solid', bd=1)
        form_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # إطار داخلي للحقول
        fields_frame = tk.Frame(form_frame, bg=self.colors['white'])
        fields_frame.pack(padx=30, pady=30)

        # الحقول مع الأيقونات
        fields = [
            ("👤 الاسم *", "name"),
            ("📞 رقم الهاتف", "phone"),
            ("🏠 العنوان", "address"),
            ("🆔 رقم الهوية", "national_id")
        ]

        self.entries = {}

        for i, (label_text, field_name) in enumerate(fields):
            # تسمية الحقل
            label = tk.Label(
                fields_frame,
                text=label_text,
                font=Font(family="Segoe UI", size=12, weight="bold"),
                bg=self.colors['white'],
                fg=self.colors['primary']
            )
            label.grid(row=i, column=0, sticky="e", padx=(0, 15), pady=15)

            # حقل الإدخال
            entry = tk.Entry(
                fields_frame,
                font=self.arabic_font,
                width=35,
                relief='solid',
                bd=2,
                bg='white'
            )
            entry.grid(row=i, column=1, sticky="w", pady=15)
            self.entries[field_name] = entry

        # قائمة الضامنين
        guarantor_label = tk.Label(
            fields_frame,
            text="👥 الضامن",
            font=Font(family="Segoe UI", size=12, weight="bold"),
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        guarantor_label.grid(row=len(fields), column=0, sticky="e", padx=(0, 15), pady=15)

        self.guarantor_combo = ttk.Combobox(
            fields_frame,
            font=self.arabic_font,
            width=32,
            state="readonly"
        )
        self.guarantor_combo.grid(row=len(fields), column=1, sticky="w", pady=15)
        
        # تحميل الضامنين
        self.load_guarantors()
        
        # ملء البيانات إذا كانت متوفرة
        if customer_data:
            self.entries["name"].insert(0, customer_data[1] or "")
            self.entries["phone"].insert(0, customer_data[2] or "")
            self.entries["address"].insert(0, customer_data[3] or "")
            self.entries["national_id"].insert(0, customer_data[4] or "")
            
            # تعيين الضامن
            if customer_data[5]:
                for i, (value, text) in enumerate(self.guarantor_combo['values']):
                    if value == customer_data[5]:
                        self.guarantor_combo.current(i)
                        break
        
        # الأزرار الجميلة
        buttons_frame = tk.Frame(main_frame, bg=self.colors['light'])
        buttons_frame.pack(pady=20)

        # زر الحفظ
        save_button = tk.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_customer,
            font=Font(family="Segoe UI", size=12, weight="bold"),
            bg=self.colors['success'],
            fg='white',
            relief='flat',
            padx=30,
            pady=10,
            cursor='hand2'
        )
        save_button.pack(side=tk.LEFT, padx=10)

        # زر الإلغاء
        cancel_button = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.dialog.destroy,
            font=Font(family="Segoe UI", size=12, weight="bold"),
            bg='#e74c3c',
            fg='white',
            relief='flat',
            padx=30,
            pady=10,
            cursor='hand2'
        )
        cancel_button.pack(side=tk.LEFT, padx=10)

        # تأثيرات الهوفر للأزرار
        def on_enter_save(e):
            save_button.config(bg='#229954')
        def on_leave_save(e):
            save_button.config(bg=self.colors['success'])

        def on_enter_cancel(e):
            cancel_button.config(bg='#c0392b')
        def on_leave_cancel(e):
            cancel_button.config(bg='#e74c3c')

        save_button.bind("<Enter>", on_enter_save)
        save_button.bind("<Leave>", on_leave_save)
        cancel_button.bind("<Enter>", on_enter_cancel)
        cancel_button.bind("<Leave>", on_leave_cancel)
    
    def load_guarantors(self):
        """تحميل قائمة الضامنين"""
        conn = sqlite3.connect("motorcycle_accounting.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM guarantors ORDER BY name")
        guarantors = cursor.fetchall()
        
        # إضافة خيار فارغ
        values = [("", "بدون ضامن")]
        values.extend([(str(g[0]), g[1]) for g in guarantors])
        
        self.guarantor_combo['values'] = [f"{v[0]}|{v[1]}" for v in values]
        self.guarantor_combo.current(0)  # اختيار "بدون ضامن" افتراضياً
        
        conn.close()
    
    def save_customer(self):
        """حفظ بيانات العميل"""
        # التحقق من الحقول المطلوبة
        if not self.entries["name"].get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
            return
        
        # الحصول على ID الضامن
        guarantor_selection = self.guarantor_combo.get()
        guarantor_id = None
        if guarantor_selection and "|" in guarantor_selection:
            guarantor_id_str = guarantor_selection.split("|")[0]
            if guarantor_id_str:
                guarantor_id = int(guarantor_id_str)
        
        # إعداد النتيجة
        self.result = {
            'name': self.entries["name"].get().strip(),
            'phone': self.entries["phone"].get().strip(),
            'address': self.entries["address"].get().strip(),
            'national_id': self.entries["national_id"].get().strip(),
            'guarantor_id': guarantor_id
        }
        
        self.dialog.destroy()

class InvoiceDialog:
    def __init__(self, parent, title, invoice_data=None):
        self.result = None
        self.parent = parent

        # ألوان التصميم
        self.colors = {
            'primary': '#2c3e50',
            'secondary': '#3498db',
            'success': '#27ae60',
            'warning': '#f39c12',
            'white': '#ffffff',
            'light': '#ecf0f1'
        }

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"📄 {title}")
        self.dialog.geometry("700x600")
        self.dialog.configure(bg=self.colors['light'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # تعيين الخط
        self.arabic_font = Font(family="Segoe UI", size=11)
        self.title_font = Font(family="Segoe UI", size=16, weight="bold")
        
        self.create_widgets(invoice_data)
        
        # توسيط النافذة
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def create_widgets(self, invoice_data):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي مع خلفية جميلة
        main_frame = tk.Frame(self.dialog, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # الهيدر
        header_frame = tk.Frame(main_frame, bg=self.colors['primary'], relief='solid', bd=1)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        header_label = tk.Label(
            header_frame,
            text="📄 إنشاء فاتورة جديدة",
            font=self.title_font,
            bg=self.colors['primary'],
            fg='white'
        )
        header_label.pack(pady=15)

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg=self.colors['white'], relief='solid', bd=1)
        form_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # إطار داخلي للحقول
        fields_frame = tk.Frame(form_frame, bg=self.colors['white'])
        fields_frame.pack(padx=30, pady=30)

        # تعريف الحقول مع الأيقونات
        row = 0

        # العميل
        tk.Label(
            fields_frame,
            text="👤 العميل *",
            font=Font(family="Segoe UI", size=12, weight="bold"),
            bg=self.colors['white'],
            fg=self.colors['primary']
        ).grid(row=row, column=0, sticky="e", padx=(0, 15), pady=15)

        self.customer_combo = ttk.Combobox(fields_frame, font=self.arabic_font, width=35, state="readonly")
        self.customer_combo.grid(row=row, column=1, sticky="w", pady=15)
        row += 1

        # نوع الدراجة
        tk.Label(
            fields_frame,
            text="🏍️ نوع الدراجة *",
            font=Font(family="Segoe UI", size=12, weight="bold"),
            bg=self.colors['white'],
            fg=self.colors['primary']
        ).grid(row=row, column=0, sticky="e", padx=(0, 15), pady=15)

        self.motorcycle_entry = tk.Entry(
            fields_frame,
            font=self.arabic_font,
            width=37,
            relief='solid',
            bd=2,
            bg='white'
        )
        self.motorcycle_entry.grid(row=row, column=1, sticky="w", pady=15)
        row += 1

        # المبلغ الإجمالي
        tk.Label(
            fields_frame,
            text="💰 المبلغ الإجمالي *",
            font=Font(family="Segoe UI", size=12, weight="bold"),
            bg=self.colors['white'],
            fg=self.colors['primary']
        ).grid(row=row, column=0, sticky="e", padx=(0, 15), pady=15)

        self.total_amount_entry = tk.Entry(
            fields_frame,
            font=self.arabic_font,
            width=37,
            relief='solid',
            bd=2,
            bg='white'
        )
        self.total_amount_entry.grid(row=row, column=1, sticky="w", pady=15)
        self.total_amount_entry.bind('<KeyRelease>', self.calculate_installment)
        row += 1

        # المقدم
        tk.Label(
            fields_frame,
            text="💵 المقدم",
            font=Font(family="Segoe UI", size=12, weight="bold"),
            bg=self.colors['white'],
            fg=self.colors['primary']
        ).grid(row=row, column=0, sticky="e", padx=(0, 15), pady=15)

        self.down_payment_entry = tk.Entry(
            fields_frame,
            font=self.arabic_font,
            width=37,
            relief='solid',
            bd=2,
            bg='white'
        )
        self.down_payment_entry.grid(row=row, column=1, sticky="w", pady=15)
        self.down_payment_entry.insert(0, "0")
        self.down_payment_entry.bind('<KeyRelease>', self.calculate_installment)
        row += 1

        # عدد الأقساط
        tk.Label(
            fields_frame,
            text="📊 عدد الأقساط *",
            font=Font(family="Segoe UI", size=12, weight="bold"),
            bg=self.colors['white'],
            fg=self.colors['primary']
        ).grid(row=row, column=0, sticky="e", padx=(0, 15), pady=15)

        self.installment_count_combo = ttk.Combobox(
            fields_frame,
            font=self.arabic_font,
            width=32,
            values=["6", "12", "18", "24", "36"],
            state="readonly"
        )
        self.installment_count_combo.grid(row=row, column=1, sticky="w", pady=15)
        self.installment_count_combo.bind('<<ComboboxSelected>>', self.calculate_installment)
        row += 1

        # القسط الشهري
        tk.Label(
            fields_frame,
            text="📈 القسط الشهري",
            font=Font(family="Segoe UI", size=12, weight="bold"),
            bg=self.colors['white'],
            fg=self.colors['secondary']
        ).grid(row=row, column=0, sticky="e", padx=(0, 15), pady=15)

        self.installment_amount_entry = tk.Entry(
            fields_frame,
            font=Font(family="Segoe UI", size=12, weight="bold"),
            width=37,
            relief='solid',
            bd=2,
            bg='#f8f9fa',
            state="readonly",
            fg=self.colors['success']
        )
        self.installment_amount_entry.grid(row=row, column=1, sticky="w", pady=15)
        row += 1

        # تاريخ الفاتورة
        tk.Label(
            fields_frame,
            text="📅 تاريخ الفاتورة *",
            font=Font(family="Segoe UI", size=12, weight="bold"),
            bg=self.colors['white'],
            fg=self.colors['primary']
        ).grid(row=row, column=0, sticky="e", padx=(0, 15), pady=15)

        self.invoice_date_entry = tk.Entry(
            fields_frame,
            font=self.arabic_font,
            width=37,
            relief='solid',
            bd=2,
            bg='white'
        )
        self.invoice_date_entry.grid(row=row, column=1, sticky="w", pady=15)
        from datetime import date
        self.invoice_date_entry.insert(0, date.today().strftime("%Y-%m-%d"))
        
        # تحميل البيانات
        self.load_customers()
        
        # الأزرار الجميلة
        buttons_frame = tk.Frame(main_frame, bg=self.colors['light'])
        buttons_frame.pack(pady=20)

        # زر الحفظ
        save_button = tk.Button(
            buttons_frame,
            text="💾 حفظ الفاتورة",
            command=self.save_invoice,
            font=Font(family="Segoe UI", size=12, weight="bold"),
            bg=self.colors['success'],
            fg='white',
            relief='flat',
            padx=30,
            pady=10,
            cursor='hand2'
        )
        save_button.pack(side=tk.LEFT, padx=10)

        # زر الإلغاء
        cancel_button = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.dialog.destroy,
            font=Font(family="Segoe UI", size=12, weight="bold"),
            bg='#e74c3c',
            fg='white',
            relief='flat',
            padx=30,
            pady=10,
            cursor='hand2'
        )
        cancel_button.pack(side=tk.LEFT, padx=10)

        # تأثيرات الهوفر للأزرار
        def on_enter_save(e):
            save_button.config(bg='#229954')
        def on_leave_save(e):
            save_button.config(bg=self.colors['success'])

        def on_enter_cancel(e):
            cancel_button.config(bg='#c0392b')
        def on_leave_cancel(e):
            cancel_button.config(bg='#e74c3c')

        save_button.bind("<Enter>", on_enter_save)
        save_button.bind("<Leave>", on_leave_save)
        cancel_button.bind("<Enter>", on_enter_cancel)
        cancel_button.bind("<Leave>", on_leave_cancel)
    
    def load_customers(self):
        """تحميل قائمة العملاء"""
        conn = sqlite3.connect("motorcycle_accounting.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM customers ORDER BY name")
        customers = cursor.fetchall()
        
        values = [f"{c[0]}|{c[1]}" for c in customers]
        self.customer_combo['values'] = values
        
        if values:
            self.customer_combo.current(0)
        
        conn.close()
    
    def calculate_installment(self, event=None):
        """حساب القسط الشهري"""
        try:
            total_amount = float(self.total_amount_entry.get() or 0)
            down_payment = float(self.down_payment_entry.get() or 0)
            installment_count = int(self.installment_count_combo.get() or 0)
            
            if installment_count > 0:
                remaining_amount = total_amount - down_payment
                monthly_installment = remaining_amount / installment_count
                
                self.installment_amount_entry.config(state="normal")
                self.installment_amount_entry.delete(0, tk.END)
                self.installment_amount_entry.insert(0, f"{monthly_installment:.0f}")
                self.installment_amount_entry.config(state="readonly")
        except ValueError:
            pass
    
    def save_invoice(self):
        """حفظ بيانات الفاتورة"""
        # التحقق من الحقول المطلوبة
        if not self.customer_combo.get():
            messagebox.showerror("خطأ", "يرجى اختيار العميل")
            return
        
        if not self.motorcycle_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال نوع الدراجة")
            return
        
        if not self.total_amount_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال المبلغ الإجمالي")
            return
        
        if not self.installment_count_combo.get():
            messagebox.showerror("خطأ", "يرجى اختيار عدد الأقساط")
            return
        
        try:
            total_amount = float(self.total_amount_entry.get())
            down_payment = float(self.down_payment_entry.get() or 0)
            installment_count = int(self.installment_count_combo.get())
            installment_amount = float(self.installment_amount_entry.get() or 0)
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم رقمية صحيحة")
            return
        
        # الحصول على ID العميل
        customer_selection = self.customer_combo.get()
        customer_id = int(customer_selection.split("|")[0])
        
        # إعداد النتيجة
        self.result = {
            'customer_id': customer_id,
            'motorcycle_type': self.motorcycle_entry.get().strip(),
            'total_amount': total_amount,
            'down_payment': down_payment,
            'installment_count': installment_count,
            'installment_amount': installment_amount,
            'invoice_date': self.invoice_date_entry.get()
        }
        
        self.dialog.destroy()
