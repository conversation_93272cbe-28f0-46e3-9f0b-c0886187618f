#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نوافذ الحوار للتطبيق
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter.font import Font
import sqlite3
from datetime import datetime, date

class CustomerDialog:
    def __init__(self, parent, title, customer_data=None):
        self.result = None
        self.parent = parent
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.configure(bg='#f0f0f0')
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # تعيين الخط
        self.arabic_font = Font(family="Arial", size=11)
        
        self.create_widgets(customer_data)
        
        # توسيط النافذة
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def create_widgets(self, customer_data):
        """إنشاء عناصر النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # الحقول
        fields = [
            ("الاسم *", "name"),
            ("رقم الهاتف", "phone"),
            ("العنوان", "address"),
            ("رقم الهوية", "national_id")
        ]
        
        self.entries = {}
        
        for i, (label_text, field_name) in enumerate(fields):
            ttk.Label(main_frame, text=label_text, font=self.arabic_font).grid(
                row=i, column=0, sticky="e", padx=(0, 10), pady=5
            )
            
            entry = ttk.Entry(main_frame, font=self.arabic_font, width=30)
            entry.grid(row=i, column=1, sticky="w", pady=5)
            self.entries[field_name] = entry
        
        # قائمة الضامنين
        ttk.Label(main_frame, text="الضامن", font=self.arabic_font).grid(
            row=len(fields), column=0, sticky="e", padx=(0, 10), pady=5
        )
        
        self.guarantor_combo = ttk.Combobox(main_frame, font=self.arabic_font, width=27, state="readonly")
        self.guarantor_combo.grid(row=len(fields), column=1, sticky="w", pady=5)
        
        # تحميل الضامنين
        self.load_guarantors()
        
        # ملء البيانات إذا كانت متوفرة
        if customer_data:
            self.entries["name"].insert(0, customer_data[1] or "")
            self.entries["phone"].insert(0, customer_data[2] or "")
            self.entries["address"].insert(0, customer_data[3] or "")
            self.entries["national_id"].insert(0, customer_data[4] or "")
            
            # تعيين الضامن
            if customer_data[5]:
                for i, (value, text) in enumerate(self.guarantor_combo['values']):
                    if value == customer_data[5]:
                        self.guarantor_combo.current(i)
                        break
        
        # الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=len(fields) + 2, column=0, columnspan=2, pady=20)
        
        ttk.Button(
            buttons_frame, 
            text="حفظ", 
            command=self.save_customer
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame, 
            text="إلغاء", 
            command=self.dialog.destroy
        ).pack(side=tk.LEFT, padx=5)
    
    def load_guarantors(self):
        """تحميل قائمة الضامنين"""
        conn = sqlite3.connect("motorcycle_accounting.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM guarantors ORDER BY name")
        guarantors = cursor.fetchall()
        
        # إضافة خيار فارغ
        values = [("", "بدون ضامن")]
        values.extend([(str(g[0]), g[1]) for g in guarantors])
        
        self.guarantor_combo['values'] = [f"{v[0]}|{v[1]}" for v in values]
        self.guarantor_combo.current(0)  # اختيار "بدون ضامن" افتراضياً
        
        conn.close()
    
    def save_customer(self):
        """حفظ بيانات العميل"""
        # التحقق من الحقول المطلوبة
        if not self.entries["name"].get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
            return
        
        # الحصول على ID الضامن
        guarantor_selection = self.guarantor_combo.get()
        guarantor_id = None
        if guarantor_selection and "|" in guarantor_selection:
            guarantor_id_str = guarantor_selection.split("|")[0]
            if guarantor_id_str:
                guarantor_id = int(guarantor_id_str)
        
        # إعداد النتيجة
        self.result = {
            'name': self.entries["name"].get().strip(),
            'phone': self.entries["phone"].get().strip(),
            'address': self.entries["address"].get().strip(),
            'national_id': self.entries["national_id"].get().strip(),
            'guarantor_id': guarantor_id
        }
        
        self.dialog.destroy()

class InvoiceDialog:
    def __init__(self, parent, title, invoice_data=None):
        self.result = None
        self.parent = parent
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("600x500")
        self.dialog.configure(bg='#f0f0f0')
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # تعيين الخط
        self.arabic_font = Font(family="Arial", size=11)
        
        self.create_widgets(invoice_data)
        
        # توسيط النافذة
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def create_widgets(self, invoice_data):
        """إنشاء عناصر النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العميل
        ttk.Label(main_frame, text="العميل *", font=self.arabic_font).grid(
            row=0, column=0, sticky="e", padx=(0, 10), pady=5
        )
        
        self.customer_combo = ttk.Combobox(main_frame, font=self.arabic_font, width=30, state="readonly")
        self.customer_combo.grid(row=0, column=1, sticky="w", pady=5)
        
        # نوع الدراجة
        ttk.Label(main_frame, text="نوع الدراجة *", font=self.arabic_font).grid(
            row=1, column=0, sticky="e", padx=(0, 10), pady=5
        )
        
        self.motorcycle_entry = ttk.Entry(main_frame, font=self.arabic_font, width=30)
        self.motorcycle_entry.grid(row=1, column=1, sticky="w", pady=5)
        
        # المبلغ الإجمالي
        ttk.Label(main_frame, text="المبلغ الإجمالي *", font=self.arabic_font).grid(
            row=2, column=0, sticky="e", padx=(0, 10), pady=5
        )
        
        self.total_amount_entry = ttk.Entry(main_frame, font=self.arabic_font, width=30)
        self.total_amount_entry.grid(row=2, column=1, sticky="w", pady=5)
        self.total_amount_entry.bind('<KeyRelease>', self.calculate_installment)
        
        # المقدم
        ttk.Label(main_frame, text="المقدم", font=self.arabic_font).grid(
            row=3, column=0, sticky="e", padx=(0, 10), pady=5
        )
        
        self.down_payment_entry = ttk.Entry(main_frame, font=self.arabic_font, width=30)
        self.down_payment_entry.grid(row=3, column=1, sticky="w", pady=5)
        self.down_payment_entry.insert(0, "0")
        self.down_payment_entry.bind('<KeyRelease>', self.calculate_installment)
        
        # عدد الأقساط
        ttk.Label(main_frame, text="عدد الأقساط *", font=self.arabic_font).grid(
            row=4, column=0, sticky="e", padx=(0, 10), pady=5
        )
        
        self.installment_count_combo = ttk.Combobox(
            main_frame, 
            font=self.arabic_font, 
            width=27, 
            values=["6", "12", "18", "24", "36"],
            state="readonly"
        )
        self.installment_count_combo.grid(row=4, column=1, sticky="w", pady=5)
        self.installment_count_combo.bind('<<ComboboxSelected>>', self.calculate_installment)
        
        # القسط الشهري
        ttk.Label(main_frame, text="القسط الشهري", font=self.arabic_font).grid(
            row=5, column=0, sticky="e", padx=(0, 10), pady=5
        )
        
        self.installment_amount_entry = ttk.Entry(main_frame, font=self.arabic_font, width=30, state="readonly")
        self.installment_amount_entry.grid(row=5, column=1, sticky="w", pady=5)
        
        # تاريخ الفاتورة
        ttk.Label(main_frame, text="تاريخ الفاتورة *", font=self.arabic_font).grid(
            row=6, column=0, sticky="e", padx=(0, 10), pady=5
        )
        
        self.invoice_date_entry = ttk.Entry(main_frame, font=self.arabic_font, width=30)
        self.invoice_date_entry.grid(row=6, column=1, sticky="w", pady=5)
        self.invoice_date_entry.insert(0, date.today().strftime("%Y-%m-%d"))
        
        # تحميل البيانات
        self.load_customers()
        
        # الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=8, column=0, columnspan=2, pady=20)
        
        ttk.Button(
            buttons_frame, 
            text="حفظ", 
            command=self.save_invoice
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame, 
            text="إلغاء", 
            command=self.dialog.destroy
        ).pack(side=tk.LEFT, padx=5)
    
    def load_customers(self):
        """تحميل قائمة العملاء"""
        conn = sqlite3.connect("motorcycle_accounting.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM customers ORDER BY name")
        customers = cursor.fetchall()
        
        values = [f"{c[0]}|{c[1]}" for c in customers]
        self.customer_combo['values'] = values
        
        if values:
            self.customer_combo.current(0)
        
        conn.close()
    
    def calculate_installment(self, event=None):
        """حساب القسط الشهري"""
        try:
            total_amount = float(self.total_amount_entry.get() or 0)
            down_payment = float(self.down_payment_entry.get() or 0)
            installment_count = int(self.installment_count_combo.get() or 0)
            
            if installment_count > 0:
                remaining_amount = total_amount - down_payment
                monthly_installment = remaining_amount / installment_count
                
                self.installment_amount_entry.config(state="normal")
                self.installment_amount_entry.delete(0, tk.END)
                self.installment_amount_entry.insert(0, f"{monthly_installment:.0f}")
                self.installment_amount_entry.config(state="readonly")
        except ValueError:
            pass
    
    def save_invoice(self):
        """حفظ بيانات الفاتورة"""
        # التحقق من الحقول المطلوبة
        if not self.customer_combo.get():
            messagebox.showerror("خطأ", "يرجى اختيار العميل")
            return
        
        if not self.motorcycle_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال نوع الدراجة")
            return
        
        if not self.total_amount_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال المبلغ الإجمالي")
            return
        
        if not self.installment_count_combo.get():
            messagebox.showerror("خطأ", "يرجى اختيار عدد الأقساط")
            return
        
        try:
            total_amount = float(self.total_amount_entry.get())
            down_payment = float(self.down_payment_entry.get() or 0)
            installment_count = int(self.installment_count_combo.get())
            installment_amount = float(self.installment_amount_entry.get() or 0)
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم رقمية صحيحة")
            return
        
        # الحصول على ID العميل
        customer_selection = self.customer_combo.get()
        customer_id = int(customer_selection.split("|")[0])
        
        # إعداد النتيجة
        self.result = {
            'customer_id': customer_id,
            'motorcycle_type': self.motorcycle_entry.get().strip(),
            'total_amount': total_amount,
            'down_payment': down_payment,
            'installment_count': installment_count,
            'installment_amount': installment_amount,
            'invoice_date': self.invoice_date_entry.get()
        }
        
        self.dialog.destroy()
