#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام محاسبة مبيعات الدراجات النارية - نسخة تعمل بشكل صحيح
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter.font import Font
import sqlite3
from datetime import date
import os

class WorkingMotorcycleApp:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_database()
        self.create_widgets()
        self.load_data()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🏍️ نظام محاسبة مبيعات الدراجات النارية")
        self.root.geometry("1000x700")

        # ألوان التصميم
        self.colors = {
            'primary': '#2c3e50',
            'secondary': '#3498db',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'light': '#ecf0f1',
            'white': '#ffffff'
        }

        self.root.configure(bg=self.colors['light'])

        # الخطوط
        self.arabic_font = Font(family="Arial", size=10)
        self.title_font = Font(family="Arial", size=14, weight="bold")
        self.header_font = Font(family="Arial", size=18, weight="bold")

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        self.db_path = "working_motorcycle.db"

        # حذف قاعدة البيانات القديمة إذا كانت موجودة
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # جدول العملاء
        cursor.execute('''
            CREATE TABLE customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                motorcycle_type TEXT NOT NULL,
                total_amount REAL NOT NULL,
                down_payment REAL DEFAULT 0,
                remaining_amount REAL NOT NULL,
                invoice_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')

        conn.commit()
        conn.close()

        # إدراج بيانات تجريبية
        self.insert_sample_data()

    def insert_sample_data(self):
        """إدراج بيانات تجريبية"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # إدراج عملاء تجريبيين
        customers = [
            ("محمد أحمد العميل", "0501111111", "الرياض - حي النخيل"),
            ("سارة محمد العميل", "0502222222", "جدة - حي الصفا"),
            ("عبدالله سعد العميل", "0503333333", "الدمام - حي الفيصلية")
        ]
        cursor.executemany(
            "INSERT INTO customers (name, phone, address) VALUES (?, ?, ?)",
            customers
        )

        # إدراج فواتير تجريبية
        invoices = [
            ("INV-000001", 1, "BMW - S1000RR", 85000, 15000, 70000, "2024-01-15"),
            ("INV-000002", 2, "هوندا - CBR 150", 25000, 5000, 20000, "2024-01-16"),
            ("INV-000003", 3, "ياماها - YZF-R15", 28000, 8000, 20000, "2024-01-17")
        ]
        cursor.executemany(
            "INSERT INTO invoices (invoice_number, customer_id, motorcycle_type, total_amount, down_payment, remaining_amount, invoice_date) VALUES (?, ?, ?, ?, ?, ?, ?)",
            invoices
        )

        conn.commit()
        conn.close()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # العنوان
        title_frame = tk.Frame(main_frame, bg=self.colors['primary'], relief='raised', bd=2)
        title_frame.pack(fill=tk.X, pady=(0, 15))

        title_label = tk.Label(
            title_frame,
            text="🏍️ نظام محاسبة مبيعات الدراجات النارية",
            font=self.header_font,
            bg=self.colors['primary'],
            fg='white'
        )
        title_label.pack(pady=15)

        # الأزرار الرئيسية
        buttons_frame = tk.Frame(main_frame, bg=self.colors['white'], relief='raised', bd=1)
        buttons_frame.pack(fill=tk.X, pady=(0, 15))

        buttons_container = tk.Frame(buttons_frame, bg=self.colors['white'])
        buttons_container.pack(pady=15)

        # أزرار الوظائف
        tk.Button(
            buttons_container,
            text="👥 إدارة العملاء",
            command=self.open_customers_window,
            font=self.arabic_font,
            bg=self.colors['primary'],
            fg='white',
            padx=15,
            pady=8,
            relief='flat',
            cursor='hand2'
        ).pack(side=tk.LEFT, padx=8)

        tk.Button(
            buttons_container,
            text="📄 إدارة الفواتير",
            command=self.open_invoices_window,
            font=self.arabic_font,
            bg=self.colors['success'],
            fg='white',
            padx=15,
            pady=8,
            relief='flat',
            cursor='hand2'
        ).pack(side=tk.LEFT, padx=8)

        tk.Button(
            buttons_container,
            text="📊 الإحصائيات",
            command=self.show_statistics,
            font=self.arabic_font,
            bg=self.colors['warning'],
            fg='white',
            padx=15,
            pady=8,
            relief='flat',
            cursor='hand2'
        ).pack(side=tk.LEFT, padx=8)

        # إطار الإحصائيات السريعة
        stats_frame = tk.Frame(main_frame, bg=self.colors['white'], relief='raised', bd=1)
        stats_frame.pack(fill=tk.X, pady=(0, 15))

        stats_title = tk.Label(
            stats_frame,
            text="📊 الإحصائيات السريعة",
            font=self.title_font,
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        stats_title.pack(pady=(10, 5))

        # إحصائيات في صف واحد
        stats_container = tk.Frame(stats_frame, bg=self.colors['white'])
        stats_container.pack(pady=(0, 15))

        self.stats_labels = {}

        # عدد العملاء
        customers_frame = tk.Frame(stats_container, bg=self.colors['primary'], relief='raised', bd=1)
        customers_frame.pack(side=tk.LEFT, padx=10)
        tk.Label(customers_frame, text="👥", font=Font(size=16), bg=self.colors['primary'], fg='white').pack(pady=5)
        tk.Label(customers_frame, text="العملاء", font=self.arabic_font, bg=self.colors['primary'], fg='white').pack()
        self.stats_labels['customers'] = tk.Label(customers_frame, text="0", font=Font(size=14, weight="bold"), bg=self.colors['primary'], fg='white')
        self.stats_labels['customers'].pack(pady=5)

        # عدد الفواتير
        invoices_frame = tk.Frame(stats_container, bg=self.colors['success'], relief='raised', bd=1)
        invoices_frame.pack(side=tk.LEFT, padx=10)
        tk.Label(invoices_frame, text="📄", font=Font(size=16), bg=self.colors['success'], fg='white').pack(pady=5)
        tk.Label(invoices_frame, text="الفواتير", font=self.arabic_font, bg=self.colors['success'], fg='white').pack()
        self.stats_labels['invoices'] = tk.Label(invoices_frame, text="0", font=Font(size=14, weight="bold"), bg=self.colors['success'], fg='white')
        self.stats_labels['invoices'].pack(pady=5)

        # إجمالي المبيعات
        sales_frame = tk.Frame(stats_container, bg=self.colors['warning'], relief='raised', bd=1)
        sales_frame.pack(side=tk.LEFT, padx=10)
        tk.Label(sales_frame, text="💰", font=Font(size=16), bg=self.colors['warning'], fg='white').pack(pady=5)
        tk.Label(sales_frame, text="المبيعات", font=self.arabic_font, bg=self.colors['warning'], fg='white').pack()
        self.stats_labels['sales'] = tk.Label(sales_frame, text="0 ر.س", font=Font(size=14, weight="bold"), bg=self.colors['warning'], fg='white')
        self.stats_labels['sales'].pack(pady=5)

        # جدول آخر الفواتير
        table_frame = tk.Frame(main_frame, bg=self.colors['white'], relief='raised', bd=1)
        table_frame.pack(fill=tk.BOTH, expand=True)

        table_title = tk.Label(
            table_frame,
            text="📋 آخر الفواتير",
            font=self.title_font,
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        table_title.pack(pady=(10, 5))

        # إنشاء الجدول
        columns = ("رقم الفاتورة", "العميل", "نوع الدراجة", "المبلغ", "التاريخ")
        self.invoices_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=8)

        for col in columns:
            self.invoices_tree.heading(col, text=col)
            self.invoices_tree.column(col, width=150, anchor="center")

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        table_container = tk.Frame(table_frame, bg=self.colors['white'])
        table_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        self.invoices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def load_data(self):
        """تحميل البيانات"""
        self.load_statistics()
        self.load_recent_invoices()

    def load_statistics(self):
        """تحميل الإحصائيات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # عدد العملاء
        cursor.execute("SELECT COUNT(*) FROM customers")
        total_customers = cursor.fetchone()[0]
        self.stats_labels['customers'].config(text=str(total_customers))

        # عدد الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoices")
        total_invoices = cursor.fetchone()[0]
        self.stats_labels['invoices'].config(text=str(total_invoices))

        # إجمالي المبيعات
        cursor.execute("SELECT COALESCE(SUM(total_amount), 0) FROM invoices")
        total_sales = cursor.fetchone()[0]
        self.stats_labels['sales'].config(text=f"{total_sales:,.0f} ر.س")

        conn.close()

    def load_recent_invoices(self):
        """تحميل آخر الفواتير"""
        # مسح البيانات الحالية
        for item in self.invoices_tree.get_children():
            self.invoices_tree.delete(item)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT i.invoice_number, c.name, i.motorcycle_type, i.total_amount, i.invoice_date
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            ORDER BY i.created_at DESC
            LIMIT 10
        ''')

        invoices = cursor.fetchall()

        for invoice in invoices:
            self.invoices_tree.insert("", "end", values=(
                invoice[0],  # رقم الفاتورة
                invoice[1],  # العميل
                invoice[2],  # نوع الدراجة
                f"{invoice[3]:,.0f} ر.س",  # المبلغ
                invoice[4]   # التاريخ
            ))

        conn.close()

    def open_customers_window(self):
        """فتح نافذة إدارة العملاء"""
        customers_window = tk.Toplevel(self.root)
        customers_window.title("👥 إدارة العملاء")
        customers_window.geometry("800x600")
        customers_window.configure(bg=self.colors['light'])

        # العنوان
        title_label = tk.Label(
            customers_window,
            text="👥 إدارة العملاء",
            font=self.title_font,
            bg=self.colors['light'],
            fg=self.colors['primary']
        )
        title_label.pack(pady=15)

        # الأزرار
        buttons_frame = tk.Frame(customers_window, bg=self.colors['light'])
        buttons_frame.pack(pady=10)

        tk.Button(
            buttons_frame,
            text="➕ إضافة عميل",
            command=lambda: self.add_customer(customers_window),
            font=self.arabic_font,
            bg=self.colors['success'],
            fg='white',
            padx=12,
            pady=5,
            cursor='hand2'
        ).pack(side=tk.LEFT, padx=5)

        tk.Button(
            buttons_frame,
            text="🔄 تحديث",
            command=lambda: self.load_customers(customers_window),
            font=self.arabic_font,
            bg=self.colors['primary'],
            fg='white',
            padx=12,
            pady=5,
            cursor='hand2'
        ).pack(side=tk.LEFT, padx=5)

        # جدول العملاء
        columns = ("ID", "الاسم", "الهاتف", "العنوان")
        customers_tree = ttk.Treeview(customers_window, columns=columns, show="headings", height=15)

        for col in columns:
            customers_tree.heading(col, text=col)
            if col == "ID":
                customers_tree.column(col, width=60, anchor="center")
            else:
                customers_tree.column(col, width=180, anchor="center")

        customers_tree.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # حفظ مرجع للجدول
        customers_window.customers_tree = customers_tree

        # تحميل البيانات
        self.load_customers(customers_window)

    def load_customers(self, window):
        """تحميل بيانات العملاء"""
        tree = window.customers_tree

        # مسح البيانات الحالية
        for item in tree.get_children():
            tree.delete(item)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT id, name, phone, address FROM customers ORDER BY created_at DESC")
        customers = cursor.fetchall()

        for customer in customers:
            tree.insert("", "end", values=customer)

        conn.close()

    def add_customer(self, parent_window):
        """إضافة عميل جديد"""
        dialog = tk.Toplevel(parent_window)
        dialog.title("➕ إضافة عميل جديد")
        dialog.geometry("450x350")
        dialog.configure(bg=self.colors['light'])
        dialog.transient(parent_window)
        dialog.grab_set()

        # العنوان
        tk.Label(
            dialog,
            text="➕ إضافة عميل جديد",
            font=self.title_font,
            bg=self.colors['light'],
            fg=self.colors['primary']
        ).pack(pady=20)

        # الحقول
        fields_frame = tk.Frame(dialog, bg=self.colors['light'])
        fields_frame.pack(pady=20)

        # الاسم
        tk.Label(fields_frame, text="الاسم *:", font=self.arabic_font, bg=self.colors['light']).grid(row=0, column=0, sticky="e", padx=10, pady=10)
        name_entry = tk.Entry(fields_frame, font=self.arabic_font, width=25)
        name_entry.grid(row=0, column=1, padx=10, pady=10)

        # الهاتف
        tk.Label(fields_frame, text="الهاتف:", font=self.arabic_font, bg=self.colors['light']).grid(row=1, column=0, sticky="e", padx=10, pady=10)
        phone_entry = tk.Entry(fields_frame, font=self.arabic_font, width=25)
        phone_entry.grid(row=1, column=1, padx=10, pady=10)

        # العنوان
        tk.Label(fields_frame, text="العنوان:", font=self.arabic_font, bg=self.colors['light']).grid(row=2, column=0, sticky="e", padx=10, pady=10)
        address_entry = tk.Entry(fields_frame, font=self.arabic_font, width=25)
        address_entry.grid(row=2, column=1, padx=10, pady=10)

        # الأزرار
        buttons_frame = tk.Frame(dialog, bg=self.colors['light'])
        buttons_frame.pack(pady=20)

        def save_customer():
            if not name_entry.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                "INSERT INTO customers (name, phone, address) VALUES (?, ?, ?)",
                (name_entry.get().strip(), phone_entry.get().strip(), address_entry.get().strip())
            )

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
            dialog.destroy()
            self.load_customers(parent_window)
            self.load_data()  # تحديث الشاشة الرئيسية

        tk.Button(
            buttons_frame,
            text="💾 حفظ",
            command=save_customer,
            font=self.arabic_font,
            bg=self.colors['success'],
            fg='white',
            padx=20,
            pady=5,
            cursor='hand2'
        ).pack(side=tk.LEFT, padx=10)

        tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=dialog.destroy,
            font=self.arabic_font,
            bg=self.colors['danger'],
            fg='white',
            padx=20,
            pady=5,
            cursor='hand2'
        ).pack(side=tk.LEFT, padx=10)

    def open_invoices_window(self):
        """فتح نافذة إدارة الفواتير"""
        invoices_window = tk.Toplevel(self.root)
        invoices_window.title("📄 إدارة الفواتير")
        invoices_window.geometry("1000x700")
        invoices_window.configure(bg=self.colors['light'])

        # العنوان
        title_label = tk.Label(
            invoices_window,
            text="📄 إدارة الفواتير",
            font=self.title_font,
            bg=self.colors['light'],
            fg=self.colors['primary']
        )
        title_label.pack(pady=15)

        # الأزرار
        buttons_frame = tk.Frame(invoices_window, bg=self.colors['light'])
        buttons_frame.pack(pady=10)

        tk.Button(
            buttons_frame,
            text="➕ إنشاء فاتورة",
            command=lambda: self.add_invoice(invoices_window),
            font=self.arabic_font,
            bg=self.colors['success'],
            fg='white',
            padx=12,
            pady=5,
            cursor='hand2'
        ).pack(side=tk.LEFT, padx=5)

        tk.Button(
            buttons_frame,
            text="🔄 تحديث",
            command=lambda: self.load_invoices(invoices_window),
            font=self.arabic_font,
            bg=self.colors['primary'],
            fg='white',
            padx=12,
            pady=5,
            cursor='hand2'
        ).pack(side=tk.LEFT, padx=5)

        # جدول الفواتير
        columns = ("ID", "رقم الفاتورة", "العميل", "نوع الدراجة", "المبلغ", "المقدم", "المتبقي", "التاريخ")
        invoices_tree = ttk.Treeview(invoices_window, columns=columns, show="headings", height=15)

        for col in columns:
            invoices_tree.heading(col, text=col)
            if col == "ID":
                invoices_tree.column(col, width=50, anchor="center")
            elif col in ["المبلغ", "المقدم", "المتبقي"]:
                invoices_tree.column(col, width=100, anchor="center")
            else:
                invoices_tree.column(col, width=120, anchor="center")

        invoices_tree.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # حفظ مرجع للجدول
        invoices_window.invoices_tree = invoices_tree

        # تحميل البيانات
        self.load_invoices(invoices_window)

    def load_invoices(self, window):
        """تحميل بيانات الفواتير"""
        tree = window.invoices_tree

        # مسح البيانات الحالية
        for item in tree.get_children():
            tree.delete(item)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT i.id, i.invoice_number, c.name, i.motorcycle_type,
                   i.total_amount, i.down_payment, i.remaining_amount, i.invoice_date
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            ORDER BY i.created_at DESC
        ''')

        invoices = cursor.fetchall()

        for invoice in invoices:
            tree.insert("", "end", values=(
                invoice[0],  # ID
                invoice[1],  # رقم الفاتورة
                invoice[2],  # العميل
                invoice[3],  # نوع الدراجة
                f"{invoice[4]:,.0f}",  # المبلغ
                f"{invoice[5]:,.0f}",  # المقدم
                f"{invoice[6]:,.0f}",  # المتبقي
                invoice[7]   # التاريخ
            ))

        conn.close()

    def add_invoice(self, parent_window):
        """إضافة فاتورة جديدة"""
        dialog = tk.Toplevel(parent_window)
        dialog.title("➕ إنشاء فاتورة جديدة")
        dialog.geometry("500x450")
        dialog.configure(bg=self.colors['light'])
        dialog.transient(parent_window)
        dialog.grab_set()

        # العنوان
        tk.Label(
            dialog,
            text="➕ إنشاء فاتورة جديدة",
            font=self.title_font,
            bg=self.colors['light'],
            fg=self.colors['primary']
        ).pack(pady=20)

        # الحقول
        fields_frame = tk.Frame(dialog, bg=self.colors['light'])
        fields_frame.pack(pady=20)

        # العميل
        tk.Label(fields_frame, text="العميل *:", font=self.arabic_font, bg=self.colors['light']).grid(row=0, column=0, sticky="e", padx=10, pady=10)
        customer_combo = ttk.Combobox(fields_frame, font=self.arabic_font, width=22, state="readonly")
        customer_combo.grid(row=0, column=1, padx=10, pady=10)

        # تحميل العملاء
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id, name FROM customers ORDER BY name")
        customers = cursor.fetchall()
        conn.close()

        customer_combo['values'] = [f"{c[0]}|{c[1]}" for c in customers]
        if customers:
            customer_combo.current(0)

        # نوع الدراجة
        tk.Label(fields_frame, text="نوع الدراجة *:", font=self.arabic_font, bg=self.colors['light']).grid(row=1, column=0, sticky="e", padx=10, pady=10)
        motorcycle_entry = tk.Entry(fields_frame, font=self.arabic_font, width=25)
        motorcycle_entry.grid(row=1, column=1, padx=10, pady=10)

        # المبلغ
        tk.Label(fields_frame, text="المبلغ الإجمالي *:", font=self.arabic_font, bg=self.colors['light']).grid(row=2, column=0, sticky="e", padx=10, pady=10)
        amount_entry = tk.Entry(fields_frame, font=self.arabic_font, width=25)
        amount_entry.grid(row=2, column=1, padx=10, pady=10)

        # المقدم
        tk.Label(fields_frame, text="المقدم:", font=self.arabic_font, bg=self.colors['light']).grid(row=3, column=0, sticky="e", padx=10, pady=10)
        down_payment_entry = tk.Entry(fields_frame, font=self.arabic_font, width=25)
        down_payment_entry.grid(row=3, column=1, padx=10, pady=10)
        down_payment_entry.insert(0, "0")

        # الأزرار
        buttons_frame = tk.Frame(dialog, bg=self.colors['light'])
        buttons_frame.pack(pady=20)

        def save_invoice():
            if not customer_combo.get():
                messagebox.showerror("خطأ", "يرجى اختيار العميل")
                return

            if not motorcycle_entry.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال نوع الدراجة")
                return

            if not amount_entry.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال المبلغ")
                return

            try:
                total_amount = float(amount_entry.get())
                down_payment = float(down_payment_entry.get() or 0)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيم رقمية صحيحة")
                return

            customer_id = int(customer_combo.get().split("|")[0])
            remaining_amount = total_amount - down_payment

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء رقم فاتورة
            cursor.execute('SELECT COUNT(*) FROM invoices')
            count = cursor.fetchone()[0]
            invoice_number = f"INV-{count + 1:06d}"

            cursor.execute('''
                INSERT INTO invoices (invoice_number, customer_id, motorcycle_type,
                                    total_amount, down_payment, remaining_amount, invoice_date)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                invoice_number,
                customer_id,
                motorcycle_entry.get().strip(),
                total_amount,
                down_payment,
                remaining_amount,
                date.today().strftime("%Y-%m-%d")
            ))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", f"تم إنشاء الفاتورة {invoice_number} بنجاح")
            dialog.destroy()
            self.load_invoices(parent_window)
            self.load_data()  # تحديث الشاشة الرئيسية

        tk.Button(
            buttons_frame,
            text="💾 حفظ",
            command=save_invoice,
            font=self.arabic_font,
            bg=self.colors['success'],
            fg='white',
            padx=20,
            pady=5,
            cursor='hand2'
        ).pack(side=tk.LEFT, padx=10)

        tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=dialog.destroy,
            font=self.arabic_font,
            bg=self.colors['danger'],
            fg='white',
            padx=20,
            pady=5,
            cursor='hand2'
        ).pack(side=tk.LEFT, padx=10)

    def show_statistics(self):
        """عرض الإحصائيات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # عدد العملاء
        cursor.execute("SELECT COUNT(*) FROM customers")
        total_customers = cursor.fetchone()[0]

        # عدد الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoices")
        total_invoices = cursor.fetchone()[0]

        # إجمالي المبيعات
        cursor.execute("SELECT COALESCE(SUM(total_amount), 0) FROM invoices")
        total_sales = cursor.fetchone()[0]

        # إجمالي المقدمات
        cursor.execute("SELECT COALESCE(SUM(down_payment), 0) FROM invoices")
        total_down_payments = cursor.fetchone()[0]

        # إجمالي المتبقي
        cursor.execute("SELECT COALESCE(SUM(remaining_amount), 0) FROM invoices")
        total_remaining = cursor.fetchone()[0]

        conn.close()

        stats_text = f"""📊 إحصائيات النظام:

👥 إجمالي العملاء: {total_customers}
📄 إجمالي الفواتير: {total_invoices}
💰 إجمالي المبيعات: {total_sales:,.0f} ر.س
💵 إجمالي المقدمات: {total_down_payments:,.0f} ر.س
⏰ إجمالي المتبقي: {total_remaining:,.0f} ر.س"""

        messagebox.showinfo("📊 الإحصائيات", stats_text)

def main():
    """الوظيفة الرئيسية"""
    root = tk.Tk()
    app = WorkingMotorcycleApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()