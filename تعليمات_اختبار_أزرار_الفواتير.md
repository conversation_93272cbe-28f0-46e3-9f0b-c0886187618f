# تعليمات اختبار أزرار التعديل والتفاصيل في فواتير المبيعات

## 🎯 الهدف
التأكد من أن أزرار التعديل والتفاصيل في صفحة فواتير المبيعات تعمل بشكل صحيح.

## 🔧 التحسينات المطبقة

### 1. إضافة API Endpoints جديدة
- ✅ `GET /api/invoices/<id>` - جلب بيانات فاتورة محددة
- ✅ `PUT /api/invoices/<id>` - تحديث بيانات فاتورة
- ✅ `GET /api/invoices/<id>/installments` - جلب أقساط الفاتورة

### 2. تطوير وظيفة `viewInvoice()`
- ✅ عرض تفاصيل شاملة للفاتورة
- ✅ عرض بيانات العميل والدراجة
- ✅ عرض المبالغ المالية مع التنسيق
- ✅ أزرار إضافية للطباعة والتعديل

### 3. تطوير وظيفة `editInvoice()`
- ✅ جلب بيانات الفاتورة وملء النموذج
- ✅ تغيير عنوان المودال ونص الزر
- ✅ ربط وظيفة التحديث بالزر

### 4. إضافة وظيفة `updateInvoice()`
- ✅ تحديث بيانات الفاتورة في قاعدة البيانات
- ✅ التحقق من صحة البيانات
- ✅ إعادة تحميل الصفحة بعد التحديث

### 5. تطوير وظيفة `viewInstallments()`
- ✅ عرض جدول الأقساط
- ✅ عرض حالة كل قسط
- ✅ أزرار لتسديد الأقساط وإنشاء جدول جديد

### 6. تحسين وظيفة `printInvoice()`
- ✅ طباعة احترافية للفاتورة
- ✅ تنسيق جميل مع الخطوط العربية
- ✅ عرض جميع بيانات الفاتورة

## 📋 خطوات الاختبار

### الاختبار الأساسي:

1. **افتح النظام:**
   ```
   http://localhost:5000
   ```

2. **اذهب إلى صفحة الفواتير:**
   - اضغط على "إدارة الفواتير"
   - أو اذهب مباشرة إلى: `http://localhost:5000/invoices`

3. **اختبار زر التفاصيل:**
   - ابحث عن أي فاتورة في الجدول
   - اضغط على زر "عرض التفاصيل" (الأيقونة الزرقاء)
   - يجب أن تظهر نافذة منبثقة مع تفاصيل شاملة للفاتورة

4. **اختبار زر التعديل:**
   - اضغط على زر "تعديل" (الأيقونة الصفراء)
   - يجب أن تظهر نافذة منبثقة مع بيانات الفاتورة
   - قم بتغيير بعض البيانات
   - اضغط على "حفظ التعديلات"
   - يجب أن تظهر رسالة نجاح وتُحدث الصفحة

5. **اختبار زر الأقساط:**
   - اضغط على زر "عرض الأقساط" (الأيقونة الخضراء)
   - يجب أن تظهر نافذة مع جدول الأقساط

6. **اختبار زر الطباعة:**
   - اضغط على زر "طباعة" (الأيقونة الزرقاء الفاتحة)
   - يجب أن تفتح نافذة طباعة مع تنسيق جميل

### الاختبار المتقدم:

7. **اختبار إنشاء فاتورة جديدة:**
   - اضغط على "إنشاء فاتورة جديدة"
   - املأ البيانات
   - اضغط على "حفظ الفاتورة"
   - تأكد من إضافة الفاتورة للجدول

8. **اختبار التبديل بين الإنشاء والتعديل:**
   - اضغط على "إنشاء فاتورة جديدة"
   - أغلق النافذة
   - اضغط على "تعديل" لفاتورة موجودة
   - تأكد من ظهور بيانات الفاتورة
   - أغلق النافذة
   - اضغط على "إنشاء فاتورة جديدة" مرة أخرى
   - تأكد من أن النموذج فارغ

## 🔍 علامات النجاح

### ✅ زر التفاصيل يعمل إذا:
- النافذة المنبثقة تظهر عند الضغط على الزر
- تفاصيل الفاتورة تظهر بشكل منظم
- بيانات العميل والدراجة تظهر بوضوح
- المبالغ المالية تظهر بتنسيق صحيح

### ✅ زر التعديل يعمل إذا:
- النافذة المنبثقة تظهر مع بيانات الفاتورة
- عنوان النافذة يتغير إلى "تعديل الفاتورة"
- زر الحفظ يتغير إلى "حفظ التعديلات"
- التعديلات تُحفظ بنجاح

### ✅ زر الأقساط يعمل إذا:
- النافذة المنبثقة تظهر مع جدول الأقساط
- حالة كل قسط تظهر بوضوح
- أزرار التسديد تظهر للأقساط المستحقة

### ✅ زر الطباعة يعمل إذا:
- نافذة الطباعة تفتح
- التنسيق جميل ومنظم
- الأحرف العربية تظهر بشكل صحيح

## 🐛 استكشاف الأخطاء

### إذا لم تعمل الأزرار:

1. **افتح أدوات المطور (F12):**
   - اذهب إلى تبويب "Console"
   - ابحث عن رسائل الخطأ

2. **تحقق من رسائل التشخيص:**
   - يجب أن ترى رسائل مثل: "عرض تفاصيل الفاتورة رقم: X"
   - يجب أن ترى: "استجابة الخادم: 200"

3. **الأخطاء الشائعة:**
   - **"النموذج غير موجود"**: تحديث الصفحة
   - **"HTTP error! status: 404"**: تأكد من تشغيل الخادم
   - **"خطأ في الشبكة"**: تحقق من الاتصال

### إذا لم تظهر البيانات:

1. **تحقق من وجود فواتير:**
   - تأكد من وجود فواتير في النظام
   - أضف فاتورة جديدة للاختبار

2. **تحقق من قاعدة البيانات:**
   - شغل `python fix_issues.py` لإضافة بيانات تجريبية

## 🛠️ ملفات الاختبار

### ملف `test_invoice_buttons.html`
صفحة اختبار تفاعلية تحتوي على:
- أزرار اختبار API الفواتير
- عرض النتائج في الوقت الفعلي
- رسائل تشخيص مفصلة

**الاستخدام:**
```
افتح الملف في المتصفح واضغط على أزرار الاختبار
```

## 📞 الدعم

### إذا واجهت مشاكل:

1. **تأكد من تشغيل الخادم:**
   ```bash
   python run.py
   ```

2. **تحقق من قاعدة البيانات:**
   ```bash
   python fix_issues.py
   ```

3. **أعد تحميل الصفحة:**
   - اضغط F5 أو Ctrl+R

4. **امسح ذاكرة التخزين المؤقت:**
   - اضغط Ctrl+Shift+R

## 🎉 النتيجة المتوقعة

بعد تطبيق جميع التحسينات، يجب أن تعمل جميع أزرار الفواتير بشكل مثالي:

- ✅ **زر التفاصيل**: عرض شامل ومنظم لبيانات الفاتورة
- ✅ **زر التعديل**: تعديل سلس وحفظ فوري
- ✅ **زر الأقساط**: عرض جدول الأقساط مع الحالات
- ✅ **زر الطباعة**: طباعة احترافية مع دعم العربية
- ✅ **تجربة مستخدم ممتازة**: سرعة ووضوح في جميع العمليات

---

**🔧 تم إصلاح جميع أزرار الفواتير وهي جاهزة للاستخدام!**
