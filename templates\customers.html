{% extends "base.html" %}

{% block title %}إدارة العملاء - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-users me-2 text-primary"></i>
                إدارة العملاء
            </h2>
            <button class="btn btn-primary btn-lg" onclick="openAddCustomerModal()">
                <i class="fas fa-plus me-2"></i>
                إضافة عميل جديد
            </button>
        </div>
    </div>
</div>

<!-- شريط البحث والفلاتر -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card filter-section">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="search-box">
                            <input type="text" class="form-control" id="customerSearch" placeholder="البحث في العملاء...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="guarantorFilter">
                            <option value="">جميع الضامنين</option>
                            {% for guarantor in guarantors %}
                            <option value="{{ guarantor.id }}">{{ guarantor.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-secondary" onclick="exportTableToPDF('customersTable', 'قائمة العملاء')">
                                <i class="fas fa-file-pdf me-1"></i>
                                PDF
                            </button>
                            <button class="btn btn-outline-secondary" onclick="printTable('customersTable')">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول العملاء -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="customersTable">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>العنوان</th>
                                <th>رقم الهوية</th>
                                <th>الضامن</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    <strong>{{ customer.name }}</strong>
                                </td>
                                <td>
                                    <i class="fas fa-phone me-1 text-muted"></i>
                                    {{ customer.phone or 'غير محدد' }}
                                </td>
                                <td>{{ customer.address or 'غير محدد' }}</td>
                                <td>{{ customer.national_id or 'غير محدد' }}</td>
                                <td>
                                    {% if customer.guarantor_name %}
                                        <span class="badge bg-success">{{ customer.guarantor_name }}</span>
                                    {% else %}
                                        <span class="badge bg-warning">بدون ضامن</span>
                                    {% endif %}
                                </td>
                                <td>{{ customer.created_at[:10] }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewCustomer({{ customer.id }})" 
                                                data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="editCustomer({{ customer.id }})"
                                                data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteCustomer({{ customer.id }})"
                                                data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center text-muted py-4">
                                    <i class="fas fa-users fa-3x mb-3 d-block"></i>
                                    لا توجد عملاء مسجلين حتى الآن
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إضافة عميل جديد -->
<div class="modal fade" id="addCustomerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة عميل جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCustomerForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">اسم العميل *</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                        <div class="col-12">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" name="address" rows="2"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهوية</label>
                            <input type="text" class="form-control" name="national_id">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الضامن</label>
                            <select class="form-select" name="guarantor_id">
                                <option value="">اختر الضامن (اختياري)</option>
                                {% for guarantor in guarantors %}
                                <option value="{{ guarantor.id }}">{{ guarantor.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveCustomer()">
                    <i class="fas fa-save me-2"></i>
                    حفظ العميل
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تفعيل البحث في الجدول
searchTable('customerSearch', 'customersTable');

// فتح مودال إضافة عميل جديد
function openAddCustomerModal() {
    console.log('فتح مودال إضافة عميل جديد'); // للتشخيص

    // التأكد من إعادة تعيين النموذج
    resetCustomerForm();

    // فتح المودال
    const modalElement = document.getElementById('addCustomerModal');
    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    } else {
        console.error('مودال العميل غير موجود');
        showAlert('خطأ في فتح نافذة إضافة العميل', 'error');
    }
}

// حفظ عميل جديد
async function saveCustomer() {
    if (!validateForm('addCustomerForm')) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    const form = document.getElementById('addCustomerForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    const result = await sendData('/api/customers', data);
    if (result) {
        bootstrap.Modal.getInstance(document.getElementById('addCustomerModal')).hide();
        clearForm('addCustomerForm');
        location.reload();
    }
}

// عرض تفاصيل العميل
async function viewCustomer(customerId) {
    try {
        const response = await fetch(`/api/customers/${customerId}`);
        const data = await response.json();

        if (data.success) {
            const customer = data.customer;
            const details = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>الاسم:</strong> ${customer.name}</p>
                        <p><strong>رقم الهاتف:</strong> ${customer.phone || 'غير محدد'}</p>
                        <p><strong>رقم الهوية:</strong> ${customer.national_id || 'غير محدد'}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>العنوان:</strong> ${customer.address || 'غير محدد'}</p>
                        <p><strong>الضامن:</strong> ${customer.guarantor_name || 'بدون ضامن'}</p>
                        <p><strong>تاريخ الإضافة:</strong> ${customer.created_at}</p>
                    </div>
                </div>
            `;

            // إنشاء مودال لعرض التفاصيل
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل العميل</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">${details}</div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        } else {
            showAlert(data.message, 'error');
        }
    } catch (error) {
        showAlert('خطأ في جلب بيانات العميل', 'error');
    }
}

// تعديل العميل
async function editCustomer(customerId) {
    console.log('تعديل العميل رقم:', customerId); // للتشخيص

    try {
        showLoading(); // إظهار مؤشر التحميل

        const response = await fetch(`/api/customers/${customerId}`);
        console.log('استجابة الخادم:', response.status); // للتشخيص

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('بيانات العميل:', data); // للتشخيص

        if (data.success) {
            const customer = data.customer;

            // التأكد من وجود النموذج
            const form = document.getElementById('addCustomerForm');
            if (!form) {
                throw new Error('النموذج غير موجود');
            }

            // ملء النموذج ببيانات العميل
            const nameInput = form.querySelector('input[name="name"]');
            const phoneInput = form.querySelector('input[name="phone"]');
            const addressInput = form.querySelector('textarea[name="address"]');
            const nationalIdInput = form.querySelector('input[name="national_id"]');
            const guarantorSelect = form.querySelector('select[name="guarantor_id"]');

            if (nameInput) nameInput.value = customer.name || '';
            if (phoneInput) phoneInput.value = customer.phone || '';
            if (addressInput) addressInput.value = customer.address || '';
            if (nationalIdInput) nationalIdInput.value = customer.national_id || '';
            if (guarantorSelect) guarantorSelect.value = customer.guarantor_id || '';

            // تغيير عنوان المودال
            const modalTitle = document.querySelector('#addCustomerModal .modal-title');
            if (modalTitle) {
                modalTitle.innerHTML = '<i class="fas fa-edit me-2"></i>تعديل بيانات العميل';
            }

            // تغيير نص الزر وإضافة معرف العميل
            const saveButton = document.querySelector('#addCustomerModal .btn-primary');
            if (saveButton) {
                saveButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ التعديلات';
                saveButton.setAttribute('data-customer-id', customerId);
                saveButton.onclick = () => updateCustomer(customerId);
            }

            // إظهار المودال
            const modalElement = document.getElementById('addCustomerModal');
            if (modalElement) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            }

            hideLoading(); // إخفاء مؤشر التحميل
            showAlert('تم تحميل بيانات العميل للتعديل', 'info');

        } else {
            hideLoading();
            showAlert(data.message || 'خطأ في جلب بيانات العميل', 'error');
        }
    } catch (error) {
        hideLoading();
        console.error('خطأ في تعديل العميل:', error);
        showAlert('خطأ في جلب بيانات العميل: ' + error.message, 'error');
    }
}

// تحديث بيانات العميل
async function updateCustomer(customerId) {
    console.log('تحديث العميل رقم:', customerId); // للتشخيص

    if (!validateForm('addCustomerForm')) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    const form = document.getElementById('addCustomerForm');
    if (!form) {
        showAlert('النموذج غير موجود', 'error');
        return;
    }

    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    // التأكد من وجود البيانات المطلوبة
    if (!data.name || data.name.trim() === '') {
        showAlert('اسم العميل مطلوب', 'warning');
        return;
    }

    console.log('بيانات التحديث:', data); // للتشخيص

    try {
        showLoading(); // إظهار مؤشر التحميل

        const response = await fetch(`/api/customers/${customerId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        console.log('استجابة التحديث:', response.status); // للتشخيص

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('نتيجة التحديث:', result); // للتشخيص

        hideLoading(); // إخفاء مؤشر التحميل

        if (result.success) {
            showAlert(result.message || 'تم تحديث بيانات العميل بنجاح', 'success');

            // إغلاق المودال
            const modalInstance = bootstrap.Modal.getInstance(document.getElementById('addCustomerModal'));
            if (modalInstance) {
                modalInstance.hide();
            }

            // إعادة تعيين النموذج
            resetCustomerForm();

            // إعادة تحميل الصفحة لإظهار التحديثات
            setTimeout(() => {
                location.reload();
            }, 1000);

        } else {
            showAlert(result.message || 'خطأ في تحديث بيانات العميل', 'error');
        }
    } catch (error) {
        hideLoading();
        console.error('خطأ في تحديث العميل:', error);
        showAlert('خطأ في تحديث بيانات العميل: ' + error.message, 'error');
    }
}

// حذف العميل
async function deleteCustomer(customerId) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟\nسيتم حذف جميع البيانات المرتبطة به نهائياً.')) {
        try {
            const response = await fetch(`/api/customers/${customerId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                showAlert(result.message, 'success');
                location.reload();
            } else {
                showAlert(result.message, 'error');
            }
        } catch (error) {
            showAlert('خطأ في حذف العميل', 'error');
        }
    }
}

// إعادة تعيين نموذج العميل
function resetCustomerForm() {
    console.log('إعادة تعيين نموذج العميل'); // للتشخيص

    // إعادة تعيين عنوان المودال
    const modalTitle = document.querySelector('#addCustomerModal .modal-title');
    if (modalTitle) {
        modalTitle.innerHTML = '<i class="fas fa-user-plus me-2"></i>إضافة عميل جديد';
    }

    // إعادة تعيين زر الحفظ
    const saveButton = document.querySelector('#addCustomerModal .btn-primary');
    if (saveButton) {
        saveButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ العميل';
        saveButton.onclick = saveCustomer;
        saveButton.removeAttribute('data-customer-id');
    }

    // مسح النموذج
    clearForm('addCustomerForm');

    // إزالة أي رسائل خطأ
    const form = document.getElementById('addCustomerForm');
    if (form) {
        const invalidInputs = form.querySelectorAll('.is-invalid');
        invalidInputs.forEach(input => {
            input.classList.remove('is-invalid');
        });
    }
}

// فلترة حسب الضامن
document.getElementById('guarantorFilter').addEventListener('change', function() {
    const selectedGuarantor = this.value;
    const rows = document.querySelectorAll('#customersTable tbody tr');
    
    rows.forEach(row => {
        if (selectedGuarantor === '') {
            row.style.display = '';
        } else {
            const guarantorCell = row.cells[5];
            const hasGuarantor = guarantorCell.textContent.includes('بدون ضامن') ? false : true;
            
            if (selectedGuarantor && hasGuarantor) {
                row.style.display = '';
            } else if (!selectedGuarantor && !hasGuarantor) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }
    });
});

// إعادة تعيين النموذج عند إغلاق المودال
document.getElementById('addCustomerModal').addEventListener('hidden.bs.modal', function () {
    resetCustomerForm();
});
</script>
{% endblock %}
