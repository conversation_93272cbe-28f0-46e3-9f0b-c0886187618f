{% extends "base.html" %}

{% block title %}إدارة العملاء - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-users me-2 text-primary"></i>
                إدارة العملاء
            </h2>
            <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                <i class="fas fa-plus me-2"></i>
                إضافة عميل جديد
            </button>
        </div>
    </div>
</div>

<!-- شريط البحث والفلاتر -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card filter-section">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="search-box">
                            <input type="text" class="form-control" id="customerSearch" placeholder="البحث في العملاء...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="guarantorFilter">
                            <option value="">جميع الضامنين</option>
                            {% for guarantor in guarantors %}
                            <option value="{{ guarantor.id }}">{{ guarantor.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-secondary" onclick="exportTableToPDF('customersTable', 'قائمة العملاء')">
                                <i class="fas fa-file-pdf me-1"></i>
                                PDF
                            </button>
                            <button class="btn btn-outline-secondary" onclick="printTable('customersTable')">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول العملاء -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="customersTable">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>العنوان</th>
                                <th>رقم الهوية</th>
                                <th>الضامن</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    <strong>{{ customer.name }}</strong>
                                </td>
                                <td>
                                    <i class="fas fa-phone me-1 text-muted"></i>
                                    {{ customer.phone or 'غير محدد' }}
                                </td>
                                <td>{{ customer.address or 'غير محدد' }}</td>
                                <td>{{ customer.national_id or 'غير محدد' }}</td>
                                <td>
                                    {% if customer.guarantor_name %}
                                        <span class="badge bg-success">{{ customer.guarantor_name }}</span>
                                    {% else %}
                                        <span class="badge bg-warning">بدون ضامن</span>
                                    {% endif %}
                                </td>
                                <td>{{ customer.created_at[:10] }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewCustomer({{ customer.id }})" 
                                                data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="editCustomer({{ customer.id }})"
                                                data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteCustomer({{ customer.id }})"
                                                data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center text-muted py-4">
                                    <i class="fas fa-users fa-3x mb-3 d-block"></i>
                                    لا توجد عملاء مسجلين حتى الآن
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إضافة عميل جديد -->
<div class="modal fade" id="addCustomerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة عميل جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCustomerForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">اسم العميل *</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                        <div class="col-12">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" name="address" rows="2"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهوية</label>
                            <input type="text" class="form-control" name="national_id">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الضامن</label>
                            <select class="form-select" name="guarantor_id">
                                <option value="">اختر الضامن (اختياري)</option>
                                {% for guarantor in guarantors %}
                                <option value="{{ guarantor.id }}">{{ guarantor.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveCustomer()">
                    <i class="fas fa-save me-2"></i>
                    حفظ العميل
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تفعيل البحث في الجدول
searchTable('customerSearch', 'customersTable');

// حفظ عميل جديد
async function saveCustomer() {
    if (!validateForm('addCustomerForm')) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    const form = document.getElementById('addCustomerForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    const result = await sendData('/api/customers', data);
    if (result) {
        bootstrap.Modal.getInstance(document.getElementById('addCustomerModal')).hide();
        clearForm('addCustomerForm');
        location.reload();
    }
}

// عرض تفاصيل العميل
function viewCustomer(customerId) {
    // هنا يمكن إضافة مودال لعرض تفاصيل العميل
    showAlert('ميزة عرض التفاصيل قيد التطوير', 'info');
}

// تعديل العميل
function editCustomer(customerId) {
    // هنا يمكن إضافة مودال لتعديل بيانات العميل
    showAlert('ميزة التعديل قيد التطوير', 'info');
}

// حذف العميل
function deleteCustomer(customerId) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
        showAlert('ميزة الحذف قيد التطوير', 'info');
    }
}

// فلترة حسب الضامن
document.getElementById('guarantorFilter').addEventListener('change', function() {
    const selectedGuarantor = this.value;
    const rows = document.querySelectorAll('#customersTable tbody tr');
    
    rows.forEach(row => {
        if (selectedGuarantor === '') {
            row.style.display = '';
        } else {
            const guarantorCell = row.cells[5];
            const hasGuarantor = guarantorCell.textContent.includes('بدون ضامن') ? false : true;
            
            if (selectedGuarantor && hasGuarantor) {
                row.style.display = '';
            } else if (!selectedGuarantor && !hasGuarantor) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }
    });
});
</script>
{% endblock %}
