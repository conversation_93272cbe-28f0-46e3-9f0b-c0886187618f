#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي لقاعدة البيانات
"""

import os
import shutil
import sqlite3
from datetime import datetime
import zipfile
import json

class BackupManager:
    def __init__(self, db_path='motorcycle_sales.db', backup_dir='backups'):
        self.db_path = db_path
        self.backup_dir = backup_dir
        self.ensure_backup_dir()
    
    def ensure_backup_dir(self):
        """إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def create_backup(self, include_metadata=True):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"backup_{timestamp}"
            backup_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
            
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # إضافة قاعدة البيانات
                if os.path.exists(self.db_path):
                    zipf.write(self.db_path, 'motorcycle_sales.db')
                
                # إضافة معلومات النسخة الاحتياطية
                if include_metadata:
                    metadata = self.get_backup_metadata()
                    metadata_json = json.dumps(metadata, ensure_ascii=False, indent=2)
                    zipf.writestr('backup_info.json', metadata_json)
                
                # إضافة ملفات التكوين
                config_files = ['config.py', 'requirements.txt']
                for config_file in config_files:
                    if os.path.exists(config_file):
                        zipf.write(config_file, config_file)
            
            print(f"تم إنشاء النسخة الاحتياطية: {backup_path}")
            return backup_path
            
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None
    
    def get_backup_metadata(self):
        """الحصول على معلومات النسخة الاحتياطية"""
        metadata = {
            'backup_date': datetime.now().isoformat(),
            'database_path': self.db_path,
            'database_size': os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
            'tables_info': {}
        }
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على قائمة الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                metadata['tables_info'][table_name] = count
            
            conn.close()
            
        except Exception as e:
            metadata['error'] = str(e)
        
        return metadata
    
    def restore_backup(self, backup_path):
        """استعادة النسخة الاحتياطية"""
        try:
            if not os.path.exists(backup_path):
                print(f"ملف النسخة الاحتياطية غير موجود: {backup_path}")
                return False
            
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
            if os.path.exists(self.db_path):
                current_backup = f"{self.db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(self.db_path, current_backup)
                print(f"تم حفظ نسخة من قاعدة البيانات الحالية: {current_backup}")
            
            # استخراج النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # استخراج قاعدة البيانات
                if 'motorcycle_sales.db' in zipf.namelist():
                    zipf.extract('motorcycle_sales.db', '.')
                    print("تم استعادة قاعدة البيانات بنجاح")
                
                # عرض معلومات النسخة الاحتياطية
                if 'backup_info.json' in zipf.namelist():
                    info_content = zipf.read('backup_info.json').decode('utf-8')
                    info = json.loads(info_content)
                    print(f"تاريخ النسخة الاحتياطية: {info.get('backup_date', 'غير محدد')}")
                    print(f"معلومات الجداول: {info.get('tables_info', {})}")
            
            return True
            
        except Exception as e:
            print(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
    
    def list_backups(self):
        """عرض قائمة النسخ الاحتياطية المتاحة"""
        backups = []
        
        if not os.path.exists(self.backup_dir):
            return backups
        
        for filename in os.listdir(self.backup_dir):
            if filename.startswith('backup_') and filename.endswith('.zip'):
                filepath = os.path.join(self.backup_dir, filename)
                stat = os.stat(filepath)
                
                backup_info = {
                    'filename': filename,
                    'filepath': filepath,
                    'size': stat.st_size,
                    'created': datetime.fromtimestamp(stat.st_ctime),
                    'modified': datetime.fromtimestamp(stat.st_mtime)
                }
                
                # محاولة قراءة معلومات إضافية من النسخة الاحتياطية
                try:
                    with zipfile.ZipFile(filepath, 'r') as zipf:
                        if 'backup_info.json' in zipf.namelist():
                            info_content = zipf.read('backup_info.json').decode('utf-8')
                            metadata = json.loads(info_content)
                            backup_info['metadata'] = metadata
                except:
                    pass
                
                backups.append(backup_info)
        
        # ترتيب النسخ حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x['created'], reverse=True)
        return backups
    
    def cleanup_old_backups(self, keep_days=30):
        """حذف النسخ الاحتياطية القديمة"""
        cutoff_date = datetime.now().timestamp() - (keep_days * 24 * 60 * 60)
        deleted_count = 0
        
        for backup in self.list_backups():
            if backup['created'].timestamp() < cutoff_date:
                try:
                    os.remove(backup['filepath'])
                    print(f"تم حذف النسخة الاحتياطية القديمة: {backup['filename']}")
                    deleted_count += 1
                except Exception as e:
                    print(f"خطأ في حذف النسخة الاحتياطية {backup['filename']}: {e}")
        
        print(f"تم حذف {deleted_count} نسخة احتياطية قديمة")
        return deleted_count

def main():
    """الوظيفة الرئيسية لإدارة النسخ الاحتياطية"""
    import sys
    
    backup_manager = BackupManager()
    
    if len(sys.argv) < 2:
        print("الاستخدام:")
        print("  python backup.py create    - إنشاء نسخة احتياطية")
        print("  python backup.py list      - عرض النسخ الاحتياطية")
        print("  python backup.py restore <filename> - استعادة نسخة احتياطية")
        print("  python backup.py cleanup   - حذف النسخ القديمة")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'create':
        backup_path = backup_manager.create_backup()
        if backup_path:
            print(f"تم إنشاء النسخة الاحتياطية بنجاح: {backup_path}")
        else:
            print("فشل في إنشاء النسخة الاحتياطية")
    
    elif command == 'list':
        backups = backup_manager.list_backups()
        if backups:
            print("النسخ الاحتياطية المتاحة:")
            for backup in backups:
                size_mb = backup['size'] / (1024 * 1024)
                print(f"  {backup['filename']} - {size_mb:.2f} MB - {backup['created']}")
        else:
            print("لا توجد نسخ احتياطية")
    
    elif command == 'restore':
        if len(sys.argv) < 3:
            print("يرجى تحديد اسم ملف النسخة الاحتياطية")
            return
        
        backup_filename = sys.argv[2]
        backup_path = os.path.join(backup_manager.backup_dir, backup_filename)
        
        if backup_manager.restore_backup(backup_path):
            print("تم استعادة النسخة الاحتياطية بنجاح")
        else:
            print("فشل في استعادة النسخة الاحتياطية")
    
    elif command == 'cleanup':
        deleted_count = backup_manager.cleanup_old_backups()
        print(f"تم حذف {deleted_count} نسخة احتياطية قديمة")
    
    else:
        print(f"أمر غير معروف: {command}")

if __name__ == '__main__':
    main()
