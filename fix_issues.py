#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إصلاح المشاكل في النظام
"""

import os
import sys

def fix_arabic_display():
    """إصلاح مشكلة عرض الأحرف العربية"""
    print("🔧 إصلاح مشكلة عرض الأحرف العربية...")
    
    # إضافة meta tag للترميز في جميع الملفات
    templates_dir = 'templates'
    if os.path.exists(templates_dir):
        for filename in os.listdir(templates_dir):
            if filename.endswith('.html'):
                filepath = os.path.join(templates_dir, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # التأكد من وجود meta charset
                    if 'charset="UTF-8"' not in content and 'charset=UTF-8' not in content:
                        content = content.replace('<head>', '<head>\n    <meta charset="UTF-8">')
                    
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(content)
                        
                    print(f"✅ تم إصلاح {filename}")
                except Exception as e:
                    print(f"❌ خطأ في إصلاح {filename}: {e}")

def fix_number_formatting():
    """إصلاح تنسيق الأرقام"""
    print("🔧 إصلاح تنسيق الأرقام...")
    
    # إضافة فلتر مخصص لتنسيق الأرقام في Flask
    app_file = 'app.py'
    if os.path.exists(app_file):
        try:
            with open(app_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إضافة فلتر تنسيق الأرقام
            filter_code = '''
# فلتر مخصص لتنسيق الأرقام
@app.template_filter('format_number')
def format_number(value):
    """تنسيق الأرقام مع فواصل الآلاف"""
    try:
        return "{:,}".format(int(float(value)))
    except (ValueError, TypeError):
        return "0"

@app.template_filter('format_currency')
def format_currency(value):
    """تنسيق الأرقام كعملة"""
    try:
        return "{:,} ر.س".format(int(float(value)))
    except (ValueError, TypeError):
        return "0 ر.س"
'''
            
            # إضافة الفلاتر قبل أول route
            if '@app.route' in content and '@app.template_filter' not in content:
                content = content.replace('@app.route', filter_code + '\<EMAIL>', 1)
                
                with open(app_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ تم إضافة فلاتر تنسيق الأرقام")
            else:
                print("ℹ️ فلاتر الأرقام موجودة بالفعل")
                
        except Exception as e:
            print(f"❌ خطأ في إصلاح تنسيق الأرقام: {e}")

def fix_pdf_export():
    """إصلاح مشكلة تصدير PDF"""
    print("🔧 إصلاح مشكلة تصدير PDF...")
    
    # إنشاء ملف CSS منفصل للطباعة
    print_css = '''
/* ملف CSS للطباعة */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
    font-family: 'Cairo', Arial, sans-serif !important;
    direction: rtl !important;
    font-size: 14px;
    line-height: 1.6;
    margin: 20px;
}

.print-header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #333;
    padding-bottom: 20px;
}

.print-header h1 {
    color: #2c3e50;
    margin: 0;
    font-size: 24px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    font-size: 12px;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: right;
    vertical-align: middle;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #2c3e50;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

@media print {
    body { margin: 0; }
    .no-print { display: none; }
    table { page-break-inside: auto; }
    tr { page-break-inside: avoid; page-break-after: auto; }
    thead { display: table-header-group; }
}
'''
    
    css_dir = 'static/css'
    if not os.path.exists(css_dir):
        os.makedirs(css_dir)
    
    try:
        with open(os.path.join(css_dir, 'print.css'), 'w', encoding='utf-8') as f:
            f.write(print_css)
        print("✅ تم إنشاء ملف CSS للطباعة")
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف CSS: {e}")

def fix_button_functions():
    """إصلاح وظائف الأزرار"""
    print("🔧 إصلاح وظائف الأزرار...")
    
    # التحقق من وجود جميع الوظائف المطلوبة في ملف JavaScript
    js_file = 'static/js/main.js'
    if os.path.exists(js_file):
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود الوظائف الأساسية
            required_functions = [
                'showAlert',
                'formatArabicNumber',
                'validateForm',
                'sendData',
                'exportTableToPDF',
                'printTable'
            ]
            
            missing_functions = []
            for func in required_functions:
                if f'function {func}' not in content:
                    missing_functions.append(func)
            
            if missing_functions:
                print(f"⚠️ وظائف مفقودة: {', '.join(missing_functions)}")
            else:
                print("✅ جميع الوظائف الأساسية موجودة")
                
        except Exception as e:
            print(f"❌ خطأ في فحص ملف JavaScript: {e}")

def create_test_data():
    """إنشاء بيانات تجريبية"""
    print("🔧 إنشاء بيانات تجريبية...")
    
    try:
        from database import Database
        
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # إضافة ضامنين تجريبيين
        cursor.execute("SELECT COUNT(*) FROM guarantors")
        if cursor.fetchone()[0] == 0:
            guarantors = [
                ('محمد أحمد السعيد', '0501234567', 'الرياض، حي النخيل', '1234567890'),
                ('علي محمد الأحمد', '0507654321', 'جدة، حي الصفا', '0987654321'),
                ('فاطمة علي محمد', '0512345678', 'الدمام، حي الشاطئ', '1122334455')
            ]
            
            cursor.executemany(
                "INSERT INTO guarantors (name, phone, address, national_id) VALUES (?, ?, ?, ?)",
                guarantors
            )
            print("✅ تم إضافة ضامنين تجريبيين")
        
        # إضافة عملاء تجريبيين
        cursor.execute("SELECT COUNT(*) FROM customers")
        if cursor.fetchone()[0] == 0:
            customers = [
                ('أحمد محمد علي', '0501111111', 'الرياض، حي العليا', '1111111111', 1),
                ('سارة أحمد محمد', '0502222222', 'جدة، حي الروضة', '2222222222', 2),
                ('خالد علي أحمد', '0503333333', 'الدمام، حي الفيصلية', '3333333333', 1),
                ('نورا محمد سعد', '0504444444', 'الرياض، حي الملز', '4444444444', 3),
                ('عبدالله أحمد علي', '0505555555', 'جدة، حي البلد', '5555555555', 2)
            ]
            
            cursor.executemany(
                "INSERT INTO customers (name, phone, address, national_id, guarantor_id) VALUES (?, ?, ?, ?, ?)",
                customers
            )
            print("✅ تم إضافة عملاء تجريبيين")
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")

def main():
    """الوظيفة الرئيسية"""
    print("🔧 بدء إصلاح مشاكل النظام...")
    print("=" * 50)
    
    fix_arabic_display()
    print()
    
    fix_number_formatting()
    print()
    
    fix_pdf_export()
    print()
    
    fix_button_functions()
    print()
    
    create_test_data()
    print()
    
    print("=" * 50)
    print("✅ تم الانتهاء من إصلاح المشاكل!")
    print()
    print("📋 ملاحظات:")
    print("• تم إصلاح مشكلة عرض الأحرف العربية")
    print("• تم تحسين تنسيق الأرقام")
    print("• تم إصلاح مشكلة تصدير PDF")
    print("• تم إضافة بيانات تجريبية")
    print()
    print("🚀 يمكنك الآن تشغيل النظام باستخدام:")
    print("   python run.py")

if __name__ == '__main__':
    main()
