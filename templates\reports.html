{% extends "base.html" %}

{% block title %}التقارير - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-chart-bar me-2 text-danger"></i>
            التقارير والإحصائيات
        </h2>
    </div>
</div>

<!-- أزرار التقارير السريعة -->
<div class="row g-4 mb-5">
    <div class="col-lg-4 col-md-6">
        <div class="card report-card h-100 shadow-sm" onclick="showSalesReport()">
            <div class="card-body text-center">
                <div class="icon-wrapper mb-3">
                    <i class="fas fa-chart-line fa-3x text-success"></i>
                </div>
                <h5 class="card-title">تقرير المبيعات الإجمالية</h5>
                <p class="card-text text-muted">
                    عرض إجمالي المبيعات والفواتير مع الرسوم البيانية
                </p>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6">
        <div class="card report-card h-100 shadow-sm" onclick="showOverdueReport()">
            <div class="card-body text-center">
                <div class="icon-wrapper mb-3">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
                </div>
                <h5 class="card-title">تقرير العملاء المتأخرين</h5>
                <p class="card-text text-muted">
                    قائمة بالعملاء المتأخرين في سداد الأقساط
                </p>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6">
        <div class="card report-card h-100 shadow-sm" onclick="showInstallmentsReport()">
            <div class="card-body text-center">
                <div class="icon-wrapper mb-3">
                    <i class="fas fa-calendar-alt fa-3x text-info"></i>
                </div>
                <h5 class="card-title">تقرير الأقساط المستحقة</h5>
                <p class="card-text text-muted">
                    عرض الأقساط المستحقة والمدفوعة والمتأخرة
                </p>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6">
        <div class="card report-card h-100 shadow-sm" onclick="showMotorcycleReport()">
            <div class="card-body text-center">
                <div class="icon-wrapper mb-3">
                    <i class="fas fa-motorcycle fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">تقرير المبيعات حسب نوع الدراجة</h5>
                <p class="card-text text-muted">
                    تحليل المبيعات حسب أنواع الدراجات النارية
                </p>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6">
        <div class="card report-card h-100 shadow-sm" onclick="showGuarantorReport()">
            <div class="card-body text-center">
                <div class="icon-wrapper mb-3">
                    <i class="fas fa-user-shield fa-3x text-success"></i>
                </div>
                <h5 class="card-title">كشف حساب الضامن</h5>
                <p class="card-text text-muted">
                    تفاصيل العملاء المرتبطين بكل ضامن
                </p>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6">
        <div class="card report-card h-100 shadow-sm" onclick="showCustomerReport()">
            <div class="card-body text-center">
                <div class="icon-wrapper mb-3">
                    <i class="fas fa-user fa-3x text-info"></i>
                </div>
                <h5 class="card-title">كشف حساب العميل</h5>
                <p class="card-text text-muted">
                    تفاصيل فواتير وأقساط العميل المحدد
                </p>
            </div>
        </div>
    </div>
</div>

<!-- منطقة عرض التقارير -->
<div class="row" id="reportArea" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0" id="reportTitle">
                    <i class="fas fa-chart-bar me-2"></i>
                    عنوان التقرير
                </h5>
                <div class="btn-group">
                    <button class="btn btn-outline-secondary btn-sm" onclick="exportCurrentReport()">
                        <i class="fas fa-file-pdf me-1"></i>
                        تصدير PDF
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="printCurrentReport()">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="closeReport()">
                        <i class="fas fa-times me-1"></i>
                        إغلاق
                    </button>
                </div>
            </div>
            <div class="card-body" id="reportContent">
                <!-- محتوى التقرير سيتم إدراجه هنا -->
            </div>
        </div>
    </div>
</div>

<!-- فلاتر التقارير -->
<div class="row mb-4" id="reportFilters" style="display: none;">
    <div class="col-12">
        <div class="card filter-section">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="fromDate">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="toDate">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">العميل</label>
                        <select class="form-select" id="customerFilter">
                            <option value="">جميع العملاء</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary w-100" onclick="applyFilters()">
                            <i class="fas fa-filter me-2"></i>
                            تطبيق الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentReportType = '';

// عرض تقرير المبيعات الإجمالية
function showSalesReport() {
    currentReportType = 'sales';
    document.getElementById('reportTitle').innerHTML = '<i class="fas fa-chart-line me-2"></i>تقرير المبيعات الإجمالية';

    const content = `
        <div class="row">
            <div class="col-md-8">
                <div class="chart-container">
                    <canvas id="salesChart"></canvas>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-summary">
                    <div class="stat-item mb-3">
                        <h6>إجمالي المبيعات</h6>
                        <h3 class="text-success">1,250,000 ر.س</h3>
                    </div>
                    <div class="stat-item mb-3">
                        <h6>عدد الفواتير</h6>
                        <h3 class="text-primary">45 فاتورة</h3>
                    </div>
                    <div class="stat-item mb-3">
                        <h6>متوسط قيمة الفاتورة</h6>
                        <h3 class="text-info">27,778 ر.س</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <h6>تفاصيل المبيعات الشهرية</h6>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الشهر</th>
                                <th>عدد الفواتير</th>
                                <th>إجمالي المبيعات</th>
                                <th>متوسط الفاتورة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>يناير 2024</td>
                                <td>12</td>
                                <td>320,000 ر.س</td>
                                <td>26,667 ر.س</td>
                            </tr>
                            <tr>
                                <td>فبراير 2024</td>
                                <td>15</td>
                                <td>425,000 ر.س</td>
                                <td>28,333 ر.س</td>
                            </tr>
                            <tr>
                                <td>مارس 2024</td>
                                <td>18</td>
                                <td>505,000 ر.س</td>
                                <td>28,056 ر.س</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    showReport(content);

    // رسم الرسم البياني
    setTimeout(() => {
        drawSalesChart();
    }, 100);
}

// عرض تقرير العملاء المتأخرين
function showOverdueReport() {
    currentReportType = 'overdue';
    document.getElementById('reportTitle').innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>تقرير العملاء المتأخرين';

    const content = `
        <div class="alert alert-warning">
            <i class="fas fa-info-circle me-2"></i>
            يعرض هذا التقرير العملاء الذين تأخروا في سداد أقساطهم لأكثر من 30 يوماً
        </div>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>اسم العميل</th>
                        <th>رقم الهاتف</th>
                        <th>الضامن</th>
                        <th>المبلغ المتأخر</th>
                        <th>عدد الأقساط المتأخرة</th>
                        <th>آخر دفعة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>أحمد محمد</strong></td>
                        <td>0501234567</td>
                        <td>محمد أحمد</td>
                        <td><span class="text-danger">15,000 ر.س</span></td>
                        <td><span class="badge bg-danger">3 أقساط</span></td>
                        <td>2024-01-15</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="sendReminder()">
                                <i class="fas fa-bell"></i> إرسال تذكير
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>فاطمة علي</strong></td>
                        <td>0507654321</td>
                        <td>علي فاطمة</td>
                        <td><span class="text-danger">8,500 ر.س</span></td>
                        <td><span class="badge bg-warning">2 قسط</span></td>
                        <td>2024-02-10</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="sendReminder()">
                                <i class="fas fa-bell"></i> إرسال تذكير
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h3>85,000 ر.س</h3>
                        <p class="mb-0">إجمالي المتأخرات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h3>15 عميل</h3>
                        <p class="mb-0">عدد العملاء المتأخرين</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3>45 يوم</h3>
                        <p class="mb-0">متوسط فترة التأخير</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    showReport(content);
}

// عرض تقرير الأقساط المستحقة
function showInstallmentsReport() {
    currentReportType = 'installments';
    document.getElementById('reportTitle').innerHTML = '<i class="fas fa-calendar-alt me-2"></i>تقرير الأقساط المستحقة';

    const content = `
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>125</h4>
                        <p class="mb-0">أقساط مدفوعة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>35</h4>
                        <p class="mb-0">أقساط مستحقة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h4>18</h4>
                        <p class="mb-0">أقساط متأخرة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>178</h4>
                        <p class="mb-0">إجمالي الأقساط</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>العميل</th>
                        <th>رقم الفاتورة</th>
                        <th>رقم القسط</th>
                        <th>المبلغ</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>أحمد محمد</td>
                        <td>INV-000001</td>
                        <td>3</td>
                        <td>2,500 ر.س</td>
                        <td>2024-03-15</td>
                        <td><span class="status-badge status-pending">مستحق</span></td>
                        <td>
                            <button class="btn btn-sm btn-success" onclick="markAsPaid()">
                                <i class="fas fa-check"></i> تسديد
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td>فاطمة علي</td>
                        <td>INV-000002</td>
                        <td>2</td>
                        <td>3,000 ر.س</td>
                        <td>2024-03-10</td>
                        <td><span class="status-badge status-overdue">متأخر</span></td>
                        <td>
                            <button class="btn btn-sm btn-success" onclick="markAsPaid()">
                                <i class="fas fa-check"></i> تسديد
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;

    showReport(content);
}

// عرض تقرير المبيعات حسب نوع الدراجة
function showMotorcycleReport() {
    currentReportType = 'motorcycle';
    document.getElementById('reportTitle').innerHTML = '<i class="fas fa-motorcycle me-2"></i>تقرير المبيعات حسب نوع الدراجة';

    const content = `
        <div class="row">
            <div class="col-md-6">
                <div class="chart-container">
                    <canvas id="motorcycleChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>نوع الدراجة</th>
                                <th>عدد المبيعات</th>
                                <th>إجمالي المبيعات</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>هوندا سي بي 150</td>
                                <td>15</td>
                                <td>675,000 ر.س</td>
                                <td>35%</td>
                            </tr>
                            <tr>
                                <td>ياماها إف زد 150</td>
                                <td>12</td>
                                <td>576,000 ر.س</td>
                                <td>28%</td>
                            </tr>
                            <tr>
                                <td>كاواساكي نينجا 250</td>
                                <td>8</td>
                                <td>520,000 ر.س</td>
                                <td>25%</td>
                            </tr>
                            <tr>
                                <td>سوزوكي جي إس 125</td>
                                <td>6</td>
                                <td>252,000 ر.س</td>
                                <td>12%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    showReport(content);

    setTimeout(() => {
        drawMotorcycleChart();
    }, 100);
}

// عرض كشف حساب الضامن
function showGuarantorReport() {
    currentReportType = 'guarantor';
    document.getElementById('reportTitle').innerHTML = '<i class="fas fa-user-shield me-2"></i>كشف حساب الضامن';

    const content = `
        <div class="mb-3">
            <select class="form-select" onchange="loadGuarantorData(this.value)">
                <option value="">اختر الضامن</option>
                <option value="1">محمد أحمد</option>
                <option value="2">علي محمد</option>
                <option value="3">فاطمة علي</option>
            </select>
        </div>
        <div id="guarantorData">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                يرجى اختيار ضامن لعرض تفاصيل حسابه
            </div>
        </div>
    `;

    showReport(content);
}

// عرض كشف حساب العميل
function showCustomerReport() {
    currentReportType = 'customer';
    document.getElementById('reportTitle').innerHTML = '<i class="fas fa-user me-2"></i>كشف حساب العميل';

    const content = `
        <div class="mb-3">
            <select class="form-select" onchange="loadCustomerData(this.value)">
                <option value="">اختر العميل</option>
                <option value="1">أحمد محمد</option>
                <option value="2">فاطمة علي</option>
                <option value="3">محمد أحمد</option>
            </select>
        </div>
        <div id="customerData">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                يرجى اختيار عميل لعرض تفاصيل حسابه
            </div>
        </div>
    `;

    showReport(content);
}

// عرض التقرير
function showReport(content) {
    document.getElementById('reportContent').innerHTML = content;
    document.getElementById('reportArea').style.display = 'block';
    document.getElementById('reportFilters').style.display = 'block';

    // التمرير إلى منطقة التقرير
    document.getElementById('reportArea').scrollIntoView({ behavior: 'smooth' });
}

// إغلاق التقرير
function closeReport() {
    document.getElementById('reportArea').style.display = 'none';
    document.getElementById('reportFilters').style.display = 'none';
}

// رسم رسم بياني للمبيعات
function drawSalesChart() {
    const ctx = document.getElementById('salesChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'المبيعات (ر.س)',
                data: [320000, 425000, 505000, 380000, 450000, 520000],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'تطور المبيعات الشهرية'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// رسم رسم بياني للدراجات
function drawMotorcycleChart() {
    const ctx = document.getElementById('motorcycleChart').getContext('2d');
    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['هوندا سي بي 150', 'ياماها إف زد 150', 'كاواساكي نينجا 250', 'سوزوكي جي إس 125'],
            datasets: [{
                data: [35, 28, 25, 12],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'توزيع المبيعات حسب نوع الدراجة'
                }
            }
        }
    });
}

// تصدير التقرير الحالي
function exportCurrentReport() {
    if (currentReportType) {
        exportTableToPDF('reportContent', `تقرير_${currentReportType}`);
    }
}

// طباعة التقرير الحالي
function printCurrentReport() {
    if (currentReportType) {
        const content = document.getElementById('reportContent').innerHTML;
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>طباعة التقرير</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f2f2f2; font-weight: bold; }
                    .card { border: 1px solid #ddd; margin: 10px 0; padding: 15px; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                <h2 style="text-align: center;">${document.getElementById('reportTitle').textContent}</h2>
                ${content}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

// تطبيق الفلاتر
function applyFilters() {
    showAlert('تم تطبيق الفلاتر بنجاح', 'success');
}

// وظائف مساعدة
function sendReminder() {
    showAlert('تم إرسال التذكير بنجاح', 'success');
}

function markAsPaid() {
    showAlert('تم تسجيل الدفعة بنجاح', 'success');
}

function loadGuarantorData(guarantorId) {
    if (guarantorId) {
        const data = `
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h6>بيانات الضامن</h6>
                            <p><strong>الاسم:</strong> محمد أحمد</p>
                            <p><strong>الهاتف:</strong> 0501234567</p>
                            <p><strong>العنوان:</strong> الرياض، حي النخيل</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h6>ملخص الحساب</h6>
                            <p><strong>عدد العملاء:</strong> 5 عملاء</p>
                            <p><strong>إجمالي المبالغ:</strong> 250,000 ر.س</p>
                            <p><strong>المتأخرات:</strong> 15,000 ر.س</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>العميل</th>
                            <th>رقم الفاتورة</th>
                            <th>إجمالي المبلغ</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>أحمد محمد</td>
                            <td>INV-000001</td>
                            <td>50,000 ر.س</td>
                            <td>35,000 ر.س</td>
                            <td>15,000 ر.س</td>
                            <td><span class="status-badge status-active">نشط</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
        document.getElementById('guarantorData').innerHTML = data;
    }
}

function loadCustomerData(customerId) {
    if (customerId) {
        const data = `
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h6>بيانات العميل</h6>
                            <p><strong>الاسم:</strong> أحمد محمد</p>
                            <p><strong>الهاتف:</strong> 0501234567</p>
                            <p><strong>الضامن:</strong> محمد أحمد</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h6>ملخص الحساب</h6>
                            <p><strong>عدد الفواتير:</strong> 2 فاتورة</p>
                            <p><strong>إجمالي المبالغ:</strong> 95,000 ر.س</p>
                            <p><strong>المدفوع:</strong> 65,000 ر.س</p>
                            <p><strong>المتبقي:</strong> 30,000 ر.س</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>نوع الدراجة</th>
                            <th>إجمالي المبلغ</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                            <th>عدد الأقساط</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>INV-000001</td>
                            <td>هوندا سي بي 150</td>
                            <td>45,000 ر.س</td>
                            <td>30,000 ر.س</td>
                            <td>15,000 ر.س</td>
                            <td>12 قسط</td>
                            <td><span class="status-badge status-active">نشط</span></td>
                        </tr>
                        <tr>
                            <td>INV-000015</td>
                            <td>ياماها إف زد 150</td>
                            <td>50,000 ر.س</td>
                            <td>35,000 ر.س</td>
                            <td>15,000 ر.س</td>
                            <td>18 قسط</td>
                            <td><span class="status-badge status-active">نشط</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
        document.getElementById('customerData').innerHTML = data;
    }
}

// تعيين التواريخ الافتراضية
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

    document.getElementById('fromDate').value = firstDay.toISOString().split('T')[0];
    document.getElementById('toDate').value = today.toISOString().split('T')[0];
});
</script>
{% endblock %}