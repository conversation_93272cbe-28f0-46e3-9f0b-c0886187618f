#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام محاسبة مبيعات الدراجات النارية - تطبيق سطح المكتب
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from tkinter.font import Font
import sqlite3
from datetime import datetime, date
import os
from dialogs import CustomerDialog, InvoiceDialog

class MotorcycleAccountingApp:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_database()
        self.create_widgets()
        self.load_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🏍️ نظام محاسبة مبيعات الدراجات النارية")
        self.root.geometry("1500x900")
        self.root.state('zoomed')  # فتح النافذة بحجم كامل

        # ألوان التصميم الحديث
        self.colors = {
            'primary': '#2c3e50',      # أزرق داكن
            'secondary': '#3498db',    # أزرق فاتح
            'success': '#27ae60',      # أخضر
            'warning': '#f39c12',      # برتقالي
            'danger': '#e74c3c',       # أحمر
            'info': '#17a2b8',         # أزرق فاتح
            'light': '#ecf0f1',        # رمادي فاتح
            'dark': '#2c3e50',         # رمادي داكن
            'white': '#ffffff',        # أبيض
            'gradient_start': '#667eea', # بداية التدرج
            'gradient_end': '#764ba2'    # نهاية التدرج
        }

        # تعيين خلفية متدرجة
        self.root.configure(bg=self.colors['light'])

        # تعيين الخطوط العربية المحسنة
        self.arabic_font = Font(family="Segoe UI", size=11)
        self.title_font = Font(family="Segoe UI", size=18, weight="bold")
        self.header_font = Font(family="Segoe UI", size=24, weight="bold")
        self.button_font = Font(family="Segoe UI", size=10, weight="bold")

        # تعيين الأيقونة إذا كانت متوفرة
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # إعداد الستايل للعناصر
        self.setup_styles()

    def setup_styles(self):
        """إعداد ستايل العناصر"""
        self.style = ttk.Style()

        # تعيين ثيم حديث
        self.style.theme_use('clam')

        # ستايل الأزرار الرئيسية
        self.style.configure('Primary.TButton',
                           background=self.colors['primary'],
                           foreground='white',
                           font=self.button_font,
                           padding=(20, 10),
                           relief='flat')

        self.style.map('Primary.TButton',
                      background=[('active', self.colors['secondary']),
                                ('pressed', self.colors['dark'])])

        # ستايل الأزرار الثانوية
        self.style.configure('Success.TButton',
                           background=self.colors['success'],
                           foreground='white',
                           font=self.button_font,
                           padding=(15, 8),
                           relief='flat')

        self.style.map('Success.TButton',
                      background=[('active', '#229954'),
                                ('pressed', '#1e8449')])

        # ستايل الأزرار التحذيرية
        self.style.configure('Warning.TButton',
                           background=self.colors['warning'],
                           foreground='white',
                           font=self.button_font,
                           padding=(15, 8),
                           relief='flat')

        self.style.map('Warning.TButton',
                      background=[('active', '#e67e22'),
                                ('pressed', '#d35400')])

        # ستايل الأزرار الخطرة
        self.style.configure('Danger.TButton',
                           background=self.colors['danger'],
                           foreground='white',
                           font=self.button_font,
                           padding=(15, 8),
                           relief='flat')

        self.style.map('Danger.TButton',
                      background=[('active', '#c0392b'),
                                ('pressed', '#a93226')])

        # ستايل الإطارات
        self.style.configure('Card.TFrame',
                           background=self.colors['white'],
                           relief='solid',
                           borderwidth=1)

        # ستايل الجداول
        self.style.configure('Treeview',
                           background=self.colors['white'],
                           foreground=self.colors['dark'],
                           font=self.arabic_font,
                           rowheight=30)

        self.style.configure('Treeview.Heading',
                           background=self.colors['primary'],
                           foreground='white',
                           font=self.button_font,
                           relief='flat')

        self.style.map('Treeview.Heading',
                      background=[('active', self.colors['secondary'])])

        # ستايل حقول الإدخال
        self.style.configure('TEntry',
                           fieldbackground=self.colors['white'],
                           borderwidth=2,
                           relief='solid')

        self.style.map('TEntry',
                      focuscolor=[('focus', self.colors['secondary'])])

        # ستايل القوائم المنسدلة
        self.style.configure('TCombobox',
                           fieldbackground=self.colors['white'],
                           borderwidth=2,
                           relief='solid')

        self.style.map('TCombobox',
                      focuscolor=[('focus', self.colors['secondary'])])
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        self.db_path = "motorcycle_accounting.db"
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                national_id TEXT,
                guarantor_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guarantor_id) REFERENCES guarantors (id)
            )
        ''')
        
        # جدول الضامنين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS guarantors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                national_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول أنواع الدراجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS motorcycle_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                model TEXT,
                price REAL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                motorcycle_type_id INTEGER NOT NULL,
                total_amount REAL NOT NULL,
                down_payment REAL DEFAULT 0,
                remaining_amount REAL NOT NULL,
                installment_amount REAL DEFAULT 0,
                installment_count INTEGER DEFAULT 0,
                invoice_date DATE NOT NULL,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (motorcycle_type_id) REFERENCES motorcycle_types (id)
            )
        ''')
        
        # جدول الأقساط
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS installments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                installment_number INTEGER NOT NULL,
                amount REAL NOT NULL,
                due_date DATE NOT NULL,
                payment_date DATE,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id)
            )
        ''')
        
        # جدول سندات القبض
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS receipts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                receipt_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                amount REAL NOT NULL,
                payment_date DATE NOT NULL,
                payment_method TEXT DEFAULT 'cash',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # إدراج بيانات تجريبية إذا لم تكن موجودة
        self.insert_sample_data()
    
    def insert_sample_data(self):
        """إدراج بيانات تجريبية"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM customers")
        if cursor.fetchone()[0] == 0:
            # إدراج ضامنين
            guarantors = [
                ("أحمد محمد الضامن", "0501234567", "الرياض", "1234567890"),
                ("فاطمة علي الضامن", "0509876543", "جدة", "0987654321")
            ]
            cursor.executemany(
                "INSERT INTO guarantors (name, phone, address, national_id) VALUES (?, ?, ?, ?)",
                guarantors
            )
            
            # إدراج عملاء
            customers = [
                ("محمد أحمد العميل", "0501111111", "الرياض - حي النخيل", "1111111111", 1),
                ("سارة محمد العميل", "0502222222", "جدة - حي الصفا", "2222222222", 2),
                ("عبدالله سعد العميل", "0503333333", "الدمام - حي الفيصلية", "3333333333", 1)
            ]
            cursor.executemany(
                "INSERT INTO customers (name, phone, address, national_id, guarantor_id) VALUES (?, ?, ?, ?, ?)",
                customers
            )
            
            # إدراج أنواع دراجات
            motorcycles = [
                ("هوندا", "CBR 150", 25000),
                ("ياماها", "YZF-R15", 28000),
                ("سوزوكي", "GSX-R125", 22000),
                ("كاواساكي", "Ninja 250", 35000),
                ("BMW", "S1000RR", 85000)
            ]
            cursor.executemany(
                "INSERT INTO motorcycle_types (name, model, price) VALUES (?, ?, ?)",
                motorcycles
            )
            
            conn.commit()
        
        conn.close()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء Canvas للخلفية المتدرجة
        self.create_gradient_background()

        # الإطار الرئيسي مع تأثير الظل
        main_frame = tk.Frame(self.root, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # إنشاء الهيدر مع التصميم الجميل
        self.create_header(main_frame)

        # إنشاء لوحة الأزرار الرئيسية
        self.create_main_buttons(main_frame)

        # إنشاء قسم الإحصائيات
        self.create_statistics_section(main_frame)

        # إنشاء قسم آخر الفواتير
        self.create_recent_invoices_section(main_frame)

    def create_gradient_background(self):
        """إنشاء خلفية متدرجة"""
        canvas = tk.Canvas(self.root, highlightthickness=0)
        canvas.pack(fill=tk.BOTH, expand=True)

        # رسم التدرج
        width = 1500
        height = 900

        for i in range(height):
            # حساب اللون المتدرج
            ratio = i / height
            r1, g1, b1 = 102, 126, 234  # gradient_start
            r2, g2, b2 = 118, 75, 162   # gradient_end

            r = int(r1 + (r2 - r1) * ratio)
            g = int(g1 + (g2 - g1) * ratio)
            b = int(b1 + (b2 - b1) * ratio)

            color = f"#{r:02x}{g:02x}{b:02x}"
            canvas.create_line(0, i, width, i, fill=color, width=1)

        # وضع الكانفاس في الخلف
        canvas.lower()

    def create_header(self, parent):
        """إنشاء الهيدر الجميل"""
        # إطار الهيدر مع خلفية شفافة
        header_frame = tk.Frame(parent, bg=self.colors['white'], relief='solid', bd=1)
        header_frame.pack(fill=tk.X, pady=(0, 30))

        # إضافة تأثير الظل
        shadow_frame = tk.Frame(parent, bg='#bdc3c7', height=3)
        shadow_frame.pack(fill=tk.X, pady=(0, 5))

        # الأيقونة والعنوان
        title_container = tk.Frame(header_frame, bg=self.colors['white'])
        title_container.pack(pady=20)

        # أيقونة الدراجة النارية
        icon_label = tk.Label(
            title_container,
            text="🏍️",
            font=Font(size=48),
            bg=self.colors['white']
        )
        icon_label.pack()

        # العنوان الرئيسي
        title_label = tk.Label(
            title_container,
            text="نظام محاسبة مبيعات الدراجات النارية",
            font=self.header_font,
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        title_label.pack(pady=(10, 5))

        # العنوان الفرعي
        subtitle_label = tk.Label(
            title_container,
            text="إدارة شاملة ومتطورة لمبيعات الدراجات النارية",
            font=Font(family="Segoe UI", size=12),
            bg=self.colors['white'],
            fg=self.colors['secondary']
        )
        subtitle_label.pack()

    def create_main_buttons(self, parent):
        """إنشاء الأزرار الرئيسية الجميلة"""
        # إطار الأزرار مع تصميم كارد
        buttons_card = tk.Frame(parent, bg=self.colors['white'], relief='solid', bd=1)
        buttons_card.pack(fill=tk.X, pady=(0, 20), padx=10)

        # عنوان القسم
        section_title = tk.Label(
            buttons_card,
            text="🚀 الوظائف الرئيسية",
            font=Font(family="Segoe UI", size=16, weight="bold"),
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        section_title.pack(pady=(15, 10))

        # إطار الأزرار
        buttons_frame = tk.Frame(buttons_card, bg=self.colors['white'])
        buttons_frame.pack(pady=(0, 20))

        # تعريف الأزرار مع الأيقونات والألوان
        buttons_config = [
            ("👥 إدارة العملاء", self.open_customers_window, 'Primary.TButton', "إدارة بيانات العملاء والضامنين"),
            ("📄 إدارة الفواتير", self.open_invoices_window, 'Success.TButton', "إنشاء وإدارة فواتير المبيعات"),
            ("🧾 سندات القبض", self.open_receipts_window, 'Warning.TButton', "إدارة سندات القبض والمدفوعات"),
            ("📊 التقارير", self.open_reports_window, 'Primary.TButton', "عرض التقارير والإحصائيات"),
            ("⚙️ الإعدادات", self.open_settings_window, 'Danger.TButton', "إعدادات النظام والتخصيص")
        ]

        # إنشاء الأزرار في صفين
        for i, (text, command, style, tooltip) in enumerate(buttons_config):
            row = i // 3
            col = i % 3

            # إطار الزر مع تأثير الهوفر
            button_container = tk.Frame(buttons_frame, bg=self.colors['white'])
            button_container.grid(row=row, column=col, padx=15, pady=10)

            # الزر الرئيسي
            button = ttk.Button(
                button_container,
                text=text,
                command=command,
                style=style,
                width=20
            )
            button.pack()

            # نص التوضيح
            tooltip_label = tk.Label(
                button_container,
                text=tooltip,
                font=Font(family="Segoe UI", size=9),
                bg=self.colors['white'],
                fg=self.colors['secondary'],
                wraplength=150
            )
            tooltip_label.pack(pady=(5, 0))

    def create_statistics_section(self, parent):
        """إنشاء قسم الإحصائيات الجميل"""
        # إطار الإحصائيات مع تصميم كارد
        stats_card = tk.Frame(parent, bg=self.colors['white'], relief='solid', bd=1)
        stats_card.pack(fill=tk.X, pady=(0, 20), padx=10)

        # عنوان القسم
        section_title = tk.Label(
            stats_card,
            text="📊 الإحصائيات السريعة",
            font=Font(family="Segoe UI", size=16, weight="bold"),
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        section_title.pack(pady=(15, 10))

        # إطار الإحصائيات
        stats_frame = tk.Frame(stats_card, bg=self.colors['white'])
        stats_frame.pack(pady=(0, 20))

        # تعريف الإحصائيات مع الأيقونات والألوان
        self.stats_labels = {}
        stats_config = [
            ("👥", "إجمالي العملاء", "total_customers", self.colors['primary']),
            ("📄", "إجمالي الفواتير", "total_invoices", self.colors['success']),
            ("💰", "إجمالي المبيعات", "total_sales", self.colors['warning']),
            ("⏰", "الأقساط المستحقة", "pending_installments", self.colors['danger'])
        ]

        for i, (icon, label_text, key, color) in enumerate(stats_config):
            # إطار كل إحصائية
            stat_frame = tk.Frame(stats_frame, bg=color, relief='solid', bd=1)
            stat_frame.grid(row=i//2, column=i%2, padx=15, pady=10, sticky="ew")

            # الأيقونة
            icon_label = tk.Label(
                stat_frame,
                text=icon,
                font=Font(size=24),
                bg=color,
                fg='white'
            )
            icon_label.pack(pady=(10, 5))

            # النص
            text_label = tk.Label(
                stat_frame,
                text=label_text,
                font=Font(family="Segoe UI", size=11, weight="bold"),
                bg=color,
                fg='white'
            )
            text_label.pack()

            # القيمة
            self.stats_labels[key] = tk.Label(
                stat_frame,
                text="0",
                font=Font(family="Segoe UI", size=18, weight="bold"),
                bg=color,
                fg='white'
            )
            self.stats_labels[key].pack(pady=(5, 15))

            # تعيين عرض متساوي للأعمدة
            stats_frame.grid_columnconfigure(i%2, weight=1)

    def create_recent_invoices_section(self, parent):
        """إنشاء قسم آخر الفواتير الجميل"""
        # إطار الفواتير مع تصميم كارد
        invoices_card = tk.Frame(parent, bg=self.colors['white'], relief='solid', bd=1)
        invoices_card.pack(fill=tk.BOTH, expand=True, padx=10)

        # عنوان القسم
        section_title = tk.Label(
            invoices_card,
            text="📋 آخر الفواتير",
            font=Font(family="Segoe UI", size=16, weight="bold"),
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        section_title.pack(pady=(15, 10))

        # إطار الجدول
        table_frame = tk.Frame(invoices_card, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # إنشاء Treeview للفواتير مع التصميم المحسن
        columns = ("رقم الفاتورة", "العميل", "نوع الدراجة", "المبلغ", "التاريخ")
        self.invoices_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=12
        )

        # تعيين عناوين الأعمدة مع الأيقونات
        column_icons = {
            "رقم الفاتورة": "📄",
            "العميل": "👤",
            "نوع الدراجة": "🏍️",
            "المبلغ": "💰",
            "التاريخ": "📅"
        }

        for col in columns:
            icon = column_icons.get(col, "")
            self.invoices_tree.heading(col, text=f"{icon} {col}")
            self.invoices_tree.column(col, width=180, anchor="center")

        # شريط التمرير مع تصميم محسن
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.invoices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إضافة تأثير الألوان المتناوبة للصفوف
        self.invoices_tree.tag_configure('evenrow', background='#f8f9fa')
        self.invoices_tree.tag_configure('oddrow', background='white')
    
    def load_data(self):
        """تحميل البيانات"""
        self.load_statistics()
        self.load_recent_invoices()
    
    def load_statistics(self):
        """تحميل الإحصائيات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # عدد العملاء
        cursor.execute("SELECT COUNT(*) FROM customers")
        total_customers = cursor.fetchone()[0]
        self.stats_labels["total_customers"].config(text=str(total_customers))
        
        # عدد الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoices")
        total_invoices = cursor.fetchone()[0]
        self.stats_labels["total_invoices"].config(text=str(total_invoices))
        
        # إجمالي المبيعات
        cursor.execute("SELECT COALESCE(SUM(total_amount), 0) FROM invoices")
        total_sales = cursor.fetchone()[0]
        self.stats_labels["total_sales"].config(text=f"{total_sales:,.0f} ر.س")
        
        # الأقساط المستحقة
        cursor.execute("SELECT COUNT(*) FROM installments WHERE status = 'pending'")
        pending_installments = cursor.fetchone()[0]
        self.stats_labels["pending_installments"].config(text=str(pending_installments))
        
        conn.close()
    
    def load_recent_invoices(self):
        """تحميل آخر الفواتير"""
        # مسح البيانات الحالية
        for item in self.invoices_tree.get_children():
            self.invoices_tree.delete(item)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT i.invoice_number, c.name, 
                   COALESCE(mt.name || ' - ' || mt.model, 'غير محدد') as motorcycle,
                   i.total_amount, i.invoice_date
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            LEFT JOIN motorcycle_types mt ON i.motorcycle_type_id = mt.id
            ORDER BY i.created_at DESC
            LIMIT 10
        ''')
        
        invoices = cursor.fetchall()

        for i, invoice in enumerate(invoices):
            # تحديد لون الصف (متناوب)
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'

            self.invoices_tree.insert("", "end", values=(
                invoice[0],  # رقم الفاتورة
                invoice[1],  # العميل
                invoice[2],  # نوع الدراجة
                f"{invoice[3]:,.0f} ر.س",  # المبلغ
                invoice[4]   # التاريخ
            ), tags=(tag,))

        conn.close()

    def open_customers_window(self):
        """فتح نافذة إدارة العملاء"""
        customers_window = tk.Toplevel(self.root)
        customers_window.title("👥 إدارة العملاء")
        customers_window.geometry("1200x700")
        customers_window.configure(bg=self.colors['light'])
        customers_window.state('zoomed')

        # إطار رئيسي مع تصميم جميل
        main_frame = tk.Frame(customers_window, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # الهيدر الجميل
        header_frame = tk.Frame(main_frame, bg=self.colors['primary'], relief='solid', bd=1)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # العنوان مع الأيقونة
        title_container = tk.Frame(header_frame, bg=self.colors['primary'])
        title_container.pack(pady=15)

        title_label = tk.Label(
            title_container,
            text="👥 إدارة العملاء",
            font=Font(family="Segoe UI", size=20, weight="bold"),
            bg=self.colors['primary'],
            fg='white'
        )
        title_label.pack()

        subtitle_label = tk.Label(
            title_container,
            text="إضافة وتعديل وإدارة بيانات العملاء والضامنين",
            font=Font(family="Segoe UI", size=12),
            bg=self.colors['primary'],
            fg='white'
        )
        subtitle_label.pack(pady=(5, 0))

        # إطار الأزرار الجميل
        buttons_card = tk.Frame(main_frame, bg=self.colors['white'], relief='solid', bd=1)
        buttons_card.pack(fill=tk.X, pady=(0, 20))

        buttons_frame = tk.Frame(buttons_card, bg=self.colors['white'])
        buttons_frame.pack(pady=15)

        # الأزرار مع الأيقونات والألوان
        buttons_config = [
            ("➕ إضافة عميل جديد", lambda: self.add_customer(customers_window), 'Success.TButton'),
            ("✏️ تعديل العميل", lambda: self.edit_customer(customers_window), 'Warning.TButton'),
            ("🗑️ حذف العميل", lambda: self.delete_customer(customers_window), 'Danger.TButton'),
            ("🔄 تحديث", lambda: self.load_customers(customers_window), 'Primary.TButton')
        ]

        for text, command, style in buttons_config:
            ttk.Button(
                buttons_frame,
                text=text,
                command=command,
                style=style,
                width=18
            ).pack(side=tk.LEFT, padx=10)

        # جدول العملاء الجميل
        table_card = tk.Frame(main_frame, bg=self.colors['white'], relief='solid', bd=1)
        table_card.pack(fill=tk.BOTH, expand=True)

        # عنوان الجدول
        table_title = tk.Label(
            table_card,
            text="📋 قائمة العملاء",
            font=Font(family="Segoe UI", size=16, weight="bold"),
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        table_title.pack(pady=(15, 10))

        # إطار الجدول
        customers_frame = tk.Frame(table_card, bg=self.colors['white'])
        customers_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # تعريف الأعمدة مع الأيقونات
        columns = ("ID", "الاسم", "الهاتف", "العنوان", "الهوية", "الضامن")
        column_icons = {
            "ID": "🆔",
            "الاسم": "👤",
            "الهاتف": "📞",
            "العنوان": "🏠",
            "الهوية": "🆔",
            "الضامن": "👥"
        }

        customers_tree = ttk.Treeview(customers_frame, columns=columns, show="headings", height=15)

        for col in columns:
            icon = column_icons.get(col, "")
            customers_tree.heading(col, text=f"{icon} {col}")
            if col == "ID":
                customers_tree.column(col, width=80, anchor="center")
            elif col in ["الاسم", "العنوان"]:
                customers_tree.column(col, width=200, anchor="center")
            else:
                customers_tree.column(col, width=150, anchor="center")

        # شريط التمرير
        scrollbar_customers = ttk.Scrollbar(customers_frame, orient=tk.VERTICAL, command=customers_tree.yview)
        customers_tree.configure(yscrollcommand=scrollbar_customers.set)

        customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_customers.pack(side=tk.RIGHT, fill=tk.Y)

        # إضافة تأثير الألوان المتناوبة
        customers_tree.tag_configure('evenrow', background='#f8f9fa')
        customers_tree.tag_configure('oddrow', background='white')

        # حفظ مرجع للجدول
        customers_window.customers_tree = customers_tree

        # تحميل البيانات
        self.load_customers(customers_window)

    def load_customers(self, window):
        """تحميل بيانات العملاء"""
        tree = window.customers_tree

        # مسح البيانات الحالية
        for item in tree.get_children():
            tree.delete(item)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT c.id, c.name, c.phone, c.address, c.national_id,
                   COALESCE(g.name, 'بدون ضامن') as guarantor_name
            FROM customers c
            LEFT JOIN guarantors g ON c.guarantor_id = g.id
            ORDER BY c.created_at DESC
        ''')

        customers = cursor.fetchall()

        for i, customer in enumerate(customers):
            # تحديد لون الصف (متناوب)
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            tree.insert("", "end", values=customer, tags=(tag,))

        conn.close()

    def add_customer(self, parent_window):
        """إضافة عميل جديد"""
        dialog = CustomerDialog(parent_window, "إضافة عميل جديد")
        if dialog.result:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO customers (name, phone, address, national_id, guarantor_id)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                dialog.result['name'],
                dialog.result['phone'],
                dialog.result['address'],
                dialog.result['national_id'],
                dialog.result['guarantor_id'] if dialog.result['guarantor_id'] else None
            ))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
            self.load_customers(parent_window)
            self.load_data()  # تحديث الإحصائيات

    def edit_customer(self, parent_window):
        """تعديل عميل"""
        tree = parent_window.customers_tree
        selected = tree.selection()

        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return

        customer_id = tree.item(selected[0])['values'][0]

        # جلب بيانات العميل الحالية
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM customers WHERE id = ?", (customer_id,))
        customer_data = cursor.fetchone()
        conn.close()

        if customer_data:
            dialog = CustomerDialog(parent_window, "تعديل العميل", customer_data)
            if dialog.result:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE customers
                    SET name = ?, phone = ?, address = ?, national_id = ?, guarantor_id = ?
                    WHERE id = ?
                ''', (
                    dialog.result['name'],
                    dialog.result['phone'],
                    dialog.result['address'],
                    dialog.result['national_id'],
                    dialog.result['guarantor_id'] if dialog.result['guarantor_id'] else None,
                    customer_id
                ))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم تحديث بيانات العميل بنجاح")
                self.load_customers(parent_window)

    def delete_customer(self, parent_window):
        """حذف عميل"""
        tree = parent_window.customers_tree
        selected = tree.selection()

        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return

        customer_id = tree.item(selected[0])['values'][0]
        customer_name = tree.item(selected[0])['values'][1]

        # التأكيد من الحذف
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف العميل '{customer_name}'؟"):
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود فواتير للعميل
            cursor.execute("SELECT COUNT(*) FROM invoices WHERE customer_id = ?", (customer_id,))
            invoice_count = cursor.fetchone()[0]

            if invoice_count > 0:
                messagebox.showerror("خطأ", f"لا يمكن حذف العميل لأن لديه {invoice_count} فاتورة مرتبطة")
                conn.close()
                return

            cursor.execute("DELETE FROM customers WHERE id = ?", (customer_id,))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
            self.load_customers(parent_window)
            self.load_data()  # تحديث الإحصائيات

    def open_invoices_window(self):
        """فتح نافذة إدارة الفواتير"""
        invoices_window = tk.Toplevel(self.root)
        invoices_window.title("إدارة الفواتير")
        invoices_window.geometry("1200x700")
        invoices_window.configure(bg='#f0f0f0')

        # العنوان
        title_label = tk.Label(
            invoices_window,
            text="إدارة الفواتير",
            font=self.title_font,
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=10)

        # إطار الأزرار
        buttons_frame = ttk.Frame(invoices_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(
            buttons_frame,
            text="إنشاء فاتورة جديدة",
            command=lambda: self.add_invoice(invoices_window)
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            buttons_frame,
            text="تعديل الفاتورة",
            command=lambda: self.edit_invoice(invoices_window)
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            buttons_frame,
            text="عرض التفاصيل",
            command=lambda: self.view_invoice(invoices_window)
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            buttons_frame,
            text="تحديث",
            command=lambda: self.load_invoices(invoices_window)
        ).pack(side=tk.LEFT, padx=5)

        # جدول الفواتير
        invoices_frame = ttk.Frame(invoices_window)
        invoices_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        columns = ("ID", "رقم الفاتورة", "العميل", "نوع الدراجة", "المبلغ الإجمالي", "المقدم", "المتبقي", "التاريخ", "الحالة")
        invoices_tree = ttk.Treeview(invoices_frame, columns=columns, show="headings")

        for col in columns:
            invoices_tree.heading(col, text=col)
            if col == "ID":
                invoices_tree.column(col, width=50, anchor="center")
            elif col in ["المبلغ الإجمالي", "المقدم", "المتبقي"]:
                invoices_tree.column(col, width=100, anchor="center")
            else:
                invoices_tree.column(col, width=120, anchor="center")

        # شريط التمرير
        scrollbar_invoices = ttk.Scrollbar(invoices_frame, orient=tk.VERTICAL, command=invoices_tree.yview)
        invoices_tree.configure(yscrollcommand=scrollbar_invoices.set)

        invoices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_invoices.pack(side=tk.RIGHT, fill=tk.Y)

        # حفظ مرجع للجدول
        invoices_window.invoices_tree = invoices_tree

        # تحميل البيانات
        self.load_invoices(invoices_window)

    def load_invoices(self, window):
        """تحميل بيانات الفواتير"""
        tree = window.invoices_tree

        # مسح البيانات الحالية
        for item in tree.get_children():
            tree.delete(item)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT i.id, i.invoice_number, c.name,
                   COALESCE(mt.name || ' - ' || mt.model, 'غير محدد') as motorcycle,
                   i.total_amount, i.down_payment, i.remaining_amount,
                   i.invoice_date, i.status
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            LEFT JOIN motorcycle_types mt ON i.motorcycle_type_id = mt.id
            ORDER BY i.created_at DESC
        ''')

        invoices = cursor.fetchall()

        for invoice in invoices:
            status_text = {
                'active': 'نشطة',
                'completed': 'مكتملة',
                'cancelled': 'ملغية'
            }.get(invoice[8], invoice[8])

            tree.insert("", "end", values=(
                invoice[0],  # ID
                invoice[1],  # رقم الفاتورة
                invoice[2],  # العميل
                invoice[3],  # نوع الدراجة
                f"{invoice[4]:,.0f}",  # المبلغ الإجمالي
                f"{invoice[5]:,.0f}",  # المقدم
                f"{invoice[6]:,.0f}",  # المتبقي
                invoice[7],  # التاريخ
                status_text   # الحالة
            ))

        conn.close()

    def add_invoice(self, parent_window):
        """إضافة فاتورة جديدة"""
        dialog = InvoiceDialog(parent_window, "إنشاء فاتورة جديدة")
        if dialog.result:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء رقم فاتورة تلقائي
            cursor.execute('SELECT COUNT(*) FROM invoices')
            count = cursor.fetchone()[0]
            invoice_number = f"INV-{count + 1:06d}"

            # معالجة نوع الدراجة
            motorcycle_type_id = self.get_or_create_motorcycle_type(
                dialog.result['motorcycle_type'],
                dialog.result['total_amount']
            )

            # حساب المبلغ المتبقي
            remaining_amount = dialog.result['total_amount'] - dialog.result['down_payment']

            cursor.execute('''
                INSERT INTO invoices (invoice_number, customer_id, motorcycle_type_id,
                                    total_amount, down_payment, remaining_amount,
                                    installment_amount, installment_count, invoice_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                invoice_number,
                dialog.result['customer_id'],
                motorcycle_type_id,
                dialog.result['total_amount'],
                dialog.result['down_payment'],
                remaining_amount,
                dialog.result['installment_amount'],
                dialog.result['installment_count'],
                dialog.result['invoice_date']
            ))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", f"تم إنشاء الفاتورة {invoice_number} بنجاح")
            self.load_invoices(parent_window)
            self.load_data()  # تحديث الإحصائيات

    def get_or_create_motorcycle_type(self, motorcycle_text, price):
        """الحصول على أو إنشاء نوع دراجة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # البحث عن نوع موجود
        cursor.execute('''
            SELECT id FROM motorcycle_types
            WHERE LOWER(name || ' - ' || model) = LOWER(?)
        ''', (motorcycle_text,))

        existing = cursor.fetchone()
        if existing:
            conn.close()
            return existing[0]

        # إنشاء نوع جديد
        parts = motorcycle_text.split(' - ')
        name = parts[0] if len(parts) > 0 else motorcycle_text
        model = parts[1] if len(parts) > 1 else ''

        cursor.execute('''
            INSERT INTO motorcycle_types (name, model, price)
            VALUES (?, ?, ?)
        ''', (name, model, price))

        motorcycle_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return motorcycle_id

    def edit_invoice(self, parent_window):
        """تعديل فاتورة"""
        tree = parent_window.invoices_tree
        selected = tree.selection()

        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار فاتورة للتعديل")
            return

        messagebox.showinfo("قريباً", "ميزة تعديل الفواتير قيد التطوير")

    def view_invoice(self, parent_window):
        """عرض تفاصيل الفاتورة"""
        tree = parent_window.invoices_tree
        selected = tree.selection()

        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار فاتورة لعرض التفاصيل")
            return

        invoice_id = tree.item(selected[0])['values'][0]

        # جلب تفاصيل الفاتورة
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT i.*, c.name as customer_name, c.phone as customer_phone,
                   mt.name as motorcycle_name, mt.model as motorcycle_model
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            LEFT JOIN motorcycle_types mt ON i.motorcycle_type_id = mt.id
            WHERE i.id = ?
        ''', (invoice_id,))

        invoice = cursor.fetchone()
        conn.close()

        if invoice:
            self.show_invoice_details(parent_window, invoice)

    def show_invoice_details(self, parent, invoice_data):
        """عرض نافذة تفاصيل الفاتورة"""
        details_window = tk.Toplevel(parent)
        details_window.title(f"تفاصيل الفاتورة {invoice_data[1]}")
        details_window.geometry("600x500")
        details_window.configure(bg='#f0f0f0')

        main_frame = ttk.Frame(details_window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # العنوان
        title_label = tk.Label(
            main_frame,
            text=f"تفاصيل الفاتورة {invoice_data[1]}",
            font=self.title_font,
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))

        # إطار التفاصيل
        details_frame = ttk.LabelFrame(main_frame, text="بيانات الفاتورة", padding=15)
        details_frame.pack(fill=tk.X, pady=(0, 10))

        details = [
            ("رقم الفاتورة:", invoice_data[1]),
            ("العميل:", invoice_data[12]),
            ("رقم الهاتف:", invoice_data[13] or "غير محدد"),
            ("نوع الدراجة:", f"{invoice_data[14] or 'غير محدد'} - {invoice_data[15] or ''}"),
            ("المبلغ الإجمالي:", f"{invoice_data[3]:,.0f} ر.س"),
            ("المقدم:", f"{invoice_data[4]:,.0f} ر.س"),
            ("المبلغ المتبقي:", f"{invoice_data[5]:,.0f} ر.س"),
            ("القسط الشهري:", f"{invoice_data[6]:,.0f} ر.س"),
            ("عدد الأقساط:", f"{invoice_data[7]} قسط"),
            ("تاريخ الفاتورة:", invoice_data[8]),
            ("الحالة:", {
                'active': 'نشطة',
                'completed': 'مكتملة',
                'cancelled': 'ملغية'
            }.get(invoice_data[9], invoice_data[9]))
        ]

        for i, (label, value) in enumerate(details):
            row = i // 2
            col = (i % 2) * 2

            ttk.Label(details_frame, text=label, font=self.arabic_font).grid(
                row=row, column=col, sticky="e", padx=(0, 5), pady=3
            )
            ttk.Label(details_frame, text=str(value), font=self.arabic_font, foreground="blue").grid(
                row=row, column=col+1, sticky="w", padx=(5, 20), pady=3
            )

        # زر الإغلاق
        ttk.Button(
            main_frame,
            text="إغلاق",
            command=details_window.destroy
        ).pack(pady=20)

    def open_receipts_window(self):
        """فتح نافذة سندات القبض"""
        messagebox.showinfo("قريباً", "نافذة سندات القبض قيد التطوير")

    def open_reports_window(self):
        """فتح نافذة التقارير"""
        messagebox.showinfo("قريباً", "نافذة التقارير قيد التطوير")

    def open_settings_window(self):
        """فتح نافذة الإعدادات"""
        messagebox.showinfo("قريباً", "نافذة الإعدادات قيد التطوير")

def main():
    """الوظيفة الرئيسية"""
    root = tk.Tk()
    app = MotorcycleAccountingApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
