#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام محاسبة مبيعات الدراجات النارية - تطبيق سطح المكتب
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from tkinter.font import Font
import sqlite3
from datetime import datetime, date
import os
from dialogs import CustomerDialog, InvoiceDialog

class MotorcycleAccountingApp:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_database()
        self.create_widgets()
        self.load_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("نظام محاسبة مبيعات الدراجات النارية")
        self.root.geometry("1400x800")
        self.root.configure(bg='#f0f0f0')
        
        # تعيين الخط العربي
        self.arabic_font = Font(family="Arial", size=12)
        self.title_font = Font(family="Arial", size=16, weight="bold")
        
        # تعيين الأيقونة إذا كانت متوفرة
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        self.db_path = "motorcycle_accounting.db"
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                national_id TEXT,
                guarantor_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guarantor_id) REFERENCES guarantors (id)
            )
        ''')
        
        # جدول الضامنين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS guarantors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                national_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول أنواع الدراجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS motorcycle_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                model TEXT,
                price REAL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                motorcycle_type_id INTEGER NOT NULL,
                total_amount REAL NOT NULL,
                down_payment REAL DEFAULT 0,
                remaining_amount REAL NOT NULL,
                installment_amount REAL DEFAULT 0,
                installment_count INTEGER DEFAULT 0,
                invoice_date DATE NOT NULL,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (motorcycle_type_id) REFERENCES motorcycle_types (id)
            )
        ''')
        
        # جدول الأقساط
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS installments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                installment_number INTEGER NOT NULL,
                amount REAL NOT NULL,
                due_date DATE NOT NULL,
                payment_date DATE,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id)
            )
        ''')
        
        # جدول سندات القبض
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS receipts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                receipt_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                amount REAL NOT NULL,
                payment_date DATE NOT NULL,
                payment_method TEXT DEFAULT 'cash',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # إدراج بيانات تجريبية إذا لم تكن موجودة
        self.insert_sample_data()
    
    def insert_sample_data(self):
        """إدراج بيانات تجريبية"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM customers")
        if cursor.fetchone()[0] == 0:
            # إدراج ضامنين
            guarantors = [
                ("أحمد محمد الضامن", "0501234567", "الرياض", "1234567890"),
                ("فاطمة علي الضامن", "0509876543", "جدة", "0987654321")
            ]
            cursor.executemany(
                "INSERT INTO guarantors (name, phone, address, national_id) VALUES (?, ?, ?, ?)",
                guarantors
            )
            
            # إدراج عملاء
            customers = [
                ("محمد أحمد العميل", "0501111111", "الرياض - حي النخيل", "1111111111", 1),
                ("سارة محمد العميل", "0502222222", "جدة - حي الصفا", "2222222222", 2),
                ("عبدالله سعد العميل", "0503333333", "الدمام - حي الفيصلية", "3333333333", 1)
            ]
            cursor.executemany(
                "INSERT INTO customers (name, phone, address, national_id, guarantor_id) VALUES (?, ?, ?, ?, ?)",
                customers
            )
            
            # إدراج أنواع دراجات
            motorcycles = [
                ("هوندا", "CBR 150", 25000),
                ("ياماها", "YZF-R15", 28000),
                ("سوزوكي", "GSX-R125", 22000),
                ("كاواساكي", "Ninja 250", 35000),
                ("BMW", "S1000RR", 85000)
            ]
            cursor.executemany(
                "INSERT INTO motorcycle_types (name, model, price) VALUES (?, ?, ?)",
                motorcycles
            )
            
            conn.commit()
        
        conn.close()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = tk.Label(
            main_frame, 
            text="نظام محاسبة مبيعات الدراجات النارية",
            font=self.title_font,
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 20))
        
        # الأزرار الرئيسية
        ttk.Button(
            buttons_frame, 
            text="إدارة العملاء", 
            command=self.open_customers_window,
            width=15
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame, 
            text="إدارة الفواتير", 
            command=self.open_invoices_window,
            width=15
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame, 
            text="سندات القبض", 
            command=self.open_receipts_window,
            width=15
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame, 
            text="التقارير", 
            command=self.open_reports_window,
            width=15
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame, 
            text="الإعدادات", 
            command=self.open_settings_window,
            width=15
        ).pack(side=tk.LEFT, padx=5)
        
        # إطار الإحصائيات
        stats_frame = ttk.LabelFrame(main_frame, text="الإحصائيات السريعة", padding=10)
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        # إحصائيات في صفوف
        self.stats_labels = {}
        stats_data = [
            ("إجمالي العملاء", "total_customers"),
            ("إجمالي الفواتير", "total_invoices"),
            ("إجمالي المبيعات", "total_sales"),
            ("الأقساط المستحقة", "pending_installments")
        ]
        
        for i, (label_text, key) in enumerate(stats_data):
            row = i // 2
            col = i % 2
            
            frame = ttk.Frame(stats_frame)
            frame.grid(row=row, column=col, padx=20, pady=10, sticky="w")
            
            ttk.Label(frame, text=f"{label_text}:", font=self.arabic_font).pack(side=tk.LEFT)
            self.stats_labels[key] = ttk.Label(frame, text="0", font=self.title_font, foreground="blue")
            self.stats_labels[key].pack(side=tk.LEFT, padx=(10, 0))
        
        # جدول آخر الفواتير
        invoices_frame = ttk.LabelFrame(main_frame, text="آخر الفواتير", padding=10)
        invoices_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء Treeview للفواتير
        columns = ("رقم الفاتورة", "العميل", "نوع الدراجة", "المبلغ", "التاريخ")
        self.invoices_tree = ttk.Treeview(invoices_frame, columns=columns, show="headings", height=10)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.invoices_tree.heading(col, text=col)
            self.invoices_tree.column(col, width=150, anchor="center")
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(invoices_frame, orient=tk.VERTICAL, command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.invoices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def load_data(self):
        """تحميل البيانات"""
        self.load_statistics()
        self.load_recent_invoices()
    
    def load_statistics(self):
        """تحميل الإحصائيات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # عدد العملاء
        cursor.execute("SELECT COUNT(*) FROM customers")
        total_customers = cursor.fetchone()[0]
        self.stats_labels["total_customers"].config(text=str(total_customers))
        
        # عدد الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoices")
        total_invoices = cursor.fetchone()[0]
        self.stats_labels["total_invoices"].config(text=str(total_invoices))
        
        # إجمالي المبيعات
        cursor.execute("SELECT COALESCE(SUM(total_amount), 0) FROM invoices")
        total_sales = cursor.fetchone()[0]
        self.stats_labels["total_sales"].config(text=f"{total_sales:,.0f} ر.س")
        
        # الأقساط المستحقة
        cursor.execute("SELECT COUNT(*) FROM installments WHERE status = 'pending'")
        pending_installments = cursor.fetchone()[0]
        self.stats_labels["pending_installments"].config(text=str(pending_installments))
        
        conn.close()
    
    def load_recent_invoices(self):
        """تحميل آخر الفواتير"""
        # مسح البيانات الحالية
        for item in self.invoices_tree.get_children():
            self.invoices_tree.delete(item)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT i.invoice_number, c.name, 
                   COALESCE(mt.name || ' - ' || mt.model, 'غير محدد') as motorcycle,
                   i.total_amount, i.invoice_date
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            LEFT JOIN motorcycle_types mt ON i.motorcycle_type_id = mt.id
            ORDER BY i.created_at DESC
            LIMIT 10
        ''')
        
        invoices = cursor.fetchall()
        
        for invoice in invoices:
            self.invoices_tree.insert("", "end", values=(
                invoice[0],  # رقم الفاتورة
                invoice[1],  # العميل
                invoice[2],  # نوع الدراجة
                f"{invoice[3]:,.0f} ر.س",  # المبلغ
                invoice[4]   # التاريخ
            ))
        
        conn.close()

    def open_customers_window(self):
        """فتح نافذة إدارة العملاء"""
        customers_window = tk.Toplevel(self.root)
        customers_window.title("إدارة العملاء")
        customers_window.geometry("1000x600")
        customers_window.configure(bg='#f0f0f0')

        # العنوان
        title_label = tk.Label(
            customers_window,
            text="إدارة العملاء",
            font=self.title_font,
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=10)

        # إطار الأزرار
        buttons_frame = ttk.Frame(customers_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(
            buttons_frame,
            text="إضافة عميل جديد",
            command=lambda: self.add_customer(customers_window)
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            buttons_frame,
            text="تعديل العميل",
            command=lambda: self.edit_customer(customers_window)
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            buttons_frame,
            text="حذف العميل",
            command=lambda: self.delete_customer(customers_window)
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            buttons_frame,
            text="تحديث",
            command=lambda: self.load_customers(customers_window)
        ).pack(side=tk.LEFT, padx=5)

        # جدول العملاء
        customers_frame = ttk.Frame(customers_window)
        customers_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        columns = ("ID", "الاسم", "الهاتف", "العنوان", "الهوية", "الضامن")
        customers_tree = ttk.Treeview(customers_frame, columns=columns, show="headings")

        for col in columns:
            customers_tree.heading(col, text=col)
            customers_tree.column(col, width=120, anchor="center")

        # شريط التمرير
        scrollbar_customers = ttk.Scrollbar(customers_frame, orient=tk.VERTICAL, command=customers_tree.yview)
        customers_tree.configure(yscrollcommand=scrollbar_customers.set)

        customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_customers.pack(side=tk.RIGHT, fill=tk.Y)

        # حفظ مرجع للجدول
        customers_window.customers_tree = customers_tree

        # تحميل البيانات
        self.load_customers(customers_window)

    def load_customers(self, window):
        """تحميل بيانات العملاء"""
        tree = window.customers_tree

        # مسح البيانات الحالية
        for item in tree.get_children():
            tree.delete(item)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT c.id, c.name, c.phone, c.address, c.national_id,
                   COALESCE(g.name, 'بدون ضامن') as guarantor_name
            FROM customers c
            LEFT JOIN guarantors g ON c.guarantor_id = g.id
            ORDER BY c.created_at DESC
        ''')

        customers = cursor.fetchall()

        for customer in customers:
            tree.insert("", "end", values=customer)

        conn.close()

    def add_customer(self, parent_window):
        """إضافة عميل جديد"""
        dialog = CustomerDialog(parent_window, "إضافة عميل جديد")
        if dialog.result:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO customers (name, phone, address, national_id, guarantor_id)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                dialog.result['name'],
                dialog.result['phone'],
                dialog.result['address'],
                dialog.result['national_id'],
                dialog.result['guarantor_id'] if dialog.result['guarantor_id'] else None
            ))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
            self.load_customers(parent_window)
            self.load_data()  # تحديث الإحصائيات

    def edit_customer(self, parent_window):
        """تعديل عميل"""
        tree = parent_window.customers_tree
        selected = tree.selection()

        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return

        customer_id = tree.item(selected[0])['values'][0]

        # جلب بيانات العميل الحالية
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM customers WHERE id = ?", (customer_id,))
        customer_data = cursor.fetchone()
        conn.close()

        if customer_data:
            dialog = CustomerDialog(parent_window, "تعديل العميل", customer_data)
            if dialog.result:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE customers
                    SET name = ?, phone = ?, address = ?, national_id = ?, guarantor_id = ?
                    WHERE id = ?
                ''', (
                    dialog.result['name'],
                    dialog.result['phone'],
                    dialog.result['address'],
                    dialog.result['national_id'],
                    dialog.result['guarantor_id'] if dialog.result['guarantor_id'] else None,
                    customer_id
                ))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم تحديث بيانات العميل بنجاح")
                self.load_customers(parent_window)

    def delete_customer(self, parent_window):
        """حذف عميل"""
        tree = parent_window.customers_tree
        selected = tree.selection()

        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return

        customer_id = tree.item(selected[0])['values'][0]
        customer_name = tree.item(selected[0])['values'][1]

        # التأكيد من الحذف
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف العميل '{customer_name}'؟"):
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود فواتير للعميل
            cursor.execute("SELECT COUNT(*) FROM invoices WHERE customer_id = ?", (customer_id,))
            invoice_count = cursor.fetchone()[0]

            if invoice_count > 0:
                messagebox.showerror("خطأ", f"لا يمكن حذف العميل لأن لديه {invoice_count} فاتورة مرتبطة")
                conn.close()
                return

            cursor.execute("DELETE FROM customers WHERE id = ?", (customer_id,))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
            self.load_customers(parent_window)
            self.load_data()  # تحديث الإحصائيات

    def open_invoices_window(self):
        """فتح نافذة إدارة الفواتير"""
        invoices_window = tk.Toplevel(self.root)
        invoices_window.title("إدارة الفواتير")
        invoices_window.geometry("1200x700")
        invoices_window.configure(bg='#f0f0f0')

        # العنوان
        title_label = tk.Label(
            invoices_window,
            text="إدارة الفواتير",
            font=self.title_font,
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=10)

        # إطار الأزرار
        buttons_frame = ttk.Frame(invoices_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(
            buttons_frame,
            text="إنشاء فاتورة جديدة",
            command=lambda: self.add_invoice(invoices_window)
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            buttons_frame,
            text="تعديل الفاتورة",
            command=lambda: self.edit_invoice(invoices_window)
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            buttons_frame,
            text="عرض التفاصيل",
            command=lambda: self.view_invoice(invoices_window)
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            buttons_frame,
            text="تحديث",
            command=lambda: self.load_invoices(invoices_window)
        ).pack(side=tk.LEFT, padx=5)

        # جدول الفواتير
        invoices_frame = ttk.Frame(invoices_window)
        invoices_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        columns = ("ID", "رقم الفاتورة", "العميل", "نوع الدراجة", "المبلغ الإجمالي", "المقدم", "المتبقي", "التاريخ", "الحالة")
        invoices_tree = ttk.Treeview(invoices_frame, columns=columns, show="headings")

        for col in columns:
            invoices_tree.heading(col, text=col)
            if col == "ID":
                invoices_tree.column(col, width=50, anchor="center")
            elif col in ["المبلغ الإجمالي", "المقدم", "المتبقي"]:
                invoices_tree.column(col, width=100, anchor="center")
            else:
                invoices_tree.column(col, width=120, anchor="center")

        # شريط التمرير
        scrollbar_invoices = ttk.Scrollbar(invoices_frame, orient=tk.VERTICAL, command=invoices_tree.yview)
        invoices_tree.configure(yscrollcommand=scrollbar_invoices.set)

        invoices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_invoices.pack(side=tk.RIGHT, fill=tk.Y)

        # حفظ مرجع للجدول
        invoices_window.invoices_tree = invoices_tree

        # تحميل البيانات
        self.load_invoices(invoices_window)

    def load_invoices(self, window):
        """تحميل بيانات الفواتير"""
        tree = window.invoices_tree

        # مسح البيانات الحالية
        for item in tree.get_children():
            tree.delete(item)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT i.id, i.invoice_number, c.name,
                   COALESCE(mt.name || ' - ' || mt.model, 'غير محدد') as motorcycle,
                   i.total_amount, i.down_payment, i.remaining_amount,
                   i.invoice_date, i.status
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            LEFT JOIN motorcycle_types mt ON i.motorcycle_type_id = mt.id
            ORDER BY i.created_at DESC
        ''')

        invoices = cursor.fetchall()

        for invoice in invoices:
            status_text = {
                'active': 'نشطة',
                'completed': 'مكتملة',
                'cancelled': 'ملغية'
            }.get(invoice[8], invoice[8])

            tree.insert("", "end", values=(
                invoice[0],  # ID
                invoice[1],  # رقم الفاتورة
                invoice[2],  # العميل
                invoice[3],  # نوع الدراجة
                f"{invoice[4]:,.0f}",  # المبلغ الإجمالي
                f"{invoice[5]:,.0f}",  # المقدم
                f"{invoice[6]:,.0f}",  # المتبقي
                invoice[7],  # التاريخ
                status_text   # الحالة
            ))

        conn.close()

    def add_invoice(self, parent_window):
        """إضافة فاتورة جديدة"""
        dialog = InvoiceDialog(parent_window, "إنشاء فاتورة جديدة")
        if dialog.result:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء رقم فاتورة تلقائي
            cursor.execute('SELECT COUNT(*) FROM invoices')
            count = cursor.fetchone()[0]
            invoice_number = f"INV-{count + 1:06d}"

            # معالجة نوع الدراجة
            motorcycle_type_id = self.get_or_create_motorcycle_type(
                dialog.result['motorcycle_type'],
                dialog.result['total_amount']
            )

            # حساب المبلغ المتبقي
            remaining_amount = dialog.result['total_amount'] - dialog.result['down_payment']

            cursor.execute('''
                INSERT INTO invoices (invoice_number, customer_id, motorcycle_type_id,
                                    total_amount, down_payment, remaining_amount,
                                    installment_amount, installment_count, invoice_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                invoice_number,
                dialog.result['customer_id'],
                motorcycle_type_id,
                dialog.result['total_amount'],
                dialog.result['down_payment'],
                remaining_amount,
                dialog.result['installment_amount'],
                dialog.result['installment_count'],
                dialog.result['invoice_date']
            ))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", f"تم إنشاء الفاتورة {invoice_number} بنجاح")
            self.load_invoices(parent_window)
            self.load_data()  # تحديث الإحصائيات

    def get_or_create_motorcycle_type(self, motorcycle_text, price):
        """الحصول على أو إنشاء نوع دراجة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # البحث عن نوع موجود
        cursor.execute('''
            SELECT id FROM motorcycle_types
            WHERE LOWER(name || ' - ' || model) = LOWER(?)
        ''', (motorcycle_text,))

        existing = cursor.fetchone()
        if existing:
            conn.close()
            return existing[0]

        # إنشاء نوع جديد
        parts = motorcycle_text.split(' - ')
        name = parts[0] if len(parts) > 0 else motorcycle_text
        model = parts[1] if len(parts) > 1 else ''

        cursor.execute('''
            INSERT INTO motorcycle_types (name, model, price)
            VALUES (?, ?, ?)
        ''', (name, model, price))

        motorcycle_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return motorcycle_id

    def edit_invoice(self, parent_window):
        """تعديل فاتورة"""
        tree = parent_window.invoices_tree
        selected = tree.selection()

        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار فاتورة للتعديل")
            return

        messagebox.showinfo("قريباً", "ميزة تعديل الفواتير قيد التطوير")

    def view_invoice(self, parent_window):
        """عرض تفاصيل الفاتورة"""
        tree = parent_window.invoices_tree
        selected = tree.selection()

        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار فاتورة لعرض التفاصيل")
            return

        invoice_id = tree.item(selected[0])['values'][0]

        # جلب تفاصيل الفاتورة
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT i.*, c.name as customer_name, c.phone as customer_phone,
                   mt.name as motorcycle_name, mt.model as motorcycle_model
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            LEFT JOIN motorcycle_types mt ON i.motorcycle_type_id = mt.id
            WHERE i.id = ?
        ''', (invoice_id,))

        invoice = cursor.fetchone()
        conn.close()

        if invoice:
            self.show_invoice_details(parent_window, invoice)

    def show_invoice_details(self, parent, invoice_data):
        """عرض نافذة تفاصيل الفاتورة"""
        details_window = tk.Toplevel(parent)
        details_window.title(f"تفاصيل الفاتورة {invoice_data[1]}")
        details_window.geometry("600x500")
        details_window.configure(bg='#f0f0f0')

        main_frame = ttk.Frame(details_window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # العنوان
        title_label = tk.Label(
            main_frame,
            text=f"تفاصيل الفاتورة {invoice_data[1]}",
            font=self.title_font,
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))

        # إطار التفاصيل
        details_frame = ttk.LabelFrame(main_frame, text="بيانات الفاتورة", padding=15)
        details_frame.pack(fill=tk.X, pady=(0, 10))

        details = [
            ("رقم الفاتورة:", invoice_data[1]),
            ("العميل:", invoice_data[12]),
            ("رقم الهاتف:", invoice_data[13] or "غير محدد"),
            ("نوع الدراجة:", f"{invoice_data[14] or 'غير محدد'} - {invoice_data[15] or ''}"),
            ("المبلغ الإجمالي:", f"{invoice_data[3]:,.0f} ر.س"),
            ("المقدم:", f"{invoice_data[4]:,.0f} ر.س"),
            ("المبلغ المتبقي:", f"{invoice_data[5]:,.0f} ر.س"),
            ("القسط الشهري:", f"{invoice_data[6]:,.0f} ر.س"),
            ("عدد الأقساط:", f"{invoice_data[7]} قسط"),
            ("تاريخ الفاتورة:", invoice_data[8]),
            ("الحالة:", {
                'active': 'نشطة',
                'completed': 'مكتملة',
                'cancelled': 'ملغية'
            }.get(invoice_data[9], invoice_data[9]))
        ]

        for i, (label, value) in enumerate(details):
            row = i // 2
            col = (i % 2) * 2

            ttk.Label(details_frame, text=label, font=self.arabic_font).grid(
                row=row, column=col, sticky="e", padx=(0, 5), pady=3
            )
            ttk.Label(details_frame, text=str(value), font=self.arabic_font, foreground="blue").grid(
                row=row, column=col+1, sticky="w", padx=(5, 20), pady=3
            )

        # زر الإغلاق
        ttk.Button(
            main_frame,
            text="إغلاق",
            command=details_window.destroy
        ).pack(pady=20)

    def open_receipts_window(self):
        """فتح نافذة سندات القبض"""
        messagebox.showinfo("قريباً", "نافذة سندات القبض قيد التطوير")

    def open_reports_window(self):
        """فتح نافذة التقارير"""
        messagebox.showinfo("قريباً", "نافذة التقارير قيد التطوير")

    def open_settings_window(self):
        """فتح نافذة الإعدادات"""
        messagebox.showinfo("قريباً", "نافذة الإعدادات قيد التطوير")

def main():
    """الوظيفة الرئيسية"""
    root = tk.Tk()
    app = MotorcycleAccountingApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
