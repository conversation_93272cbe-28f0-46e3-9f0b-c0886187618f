# نظام محاسبة مبيعات الدراجات النارية

نظام شامل لإدارة مبيعات الدراجات النارية بنظام الأقساط، متوافق مع الهواتف الذكية وأجهزة الكمبيوتر.

## الميزات الرئيسية

### 🏠 الصفحة الرئيسية
- لوحة معلومات تفاعلية
- إحصائيات سريعة (العملاء، المبيعات، الأقساط المستحقة، المتأخرات)
- وصول سريع لجميع الأقسام

### 👥 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- ربط العملاء بالضامنين
- البحث والفلترة المتقدمة
- تصدير وطباعة قوائم العملاء

### 🛡️ إدارة الضامنين
- إدارة شاملة لبيانات الضامنين
- عرض العملاء المرتبطين بكل ضامن
- كشف حساب مفصل لكل ضامن

### 🧾 إدارة الفواتير
- إنشاء فواتير مبيعات تلقائية
- حساب الأقساط الشهرية تلقائياً
- ربط الفواتير بالعملاء وأنواع الدراجات
- متابعة حالة كل فاتورة

### 🧾 سندات القبض
- تسجيل المدفوعات والسندات
- طرق دفع متعددة (نقداً، تحويل بنكي، شيك)
- ربط السندات بالفواتير والعملاء

### 📊 التقارير الشاملة
- **تقرير المبيعات الإجمالية**: رسوم بيانية وإحصائيات مفصلة
- **تقرير العملاء المتأخرين**: قائمة بالعملاء المتأخرين مع إمكانية إرسال تذكيرات
- **تقرير الأقساط المستحقة**: متابعة حالة جميع الأقساط
- **تقرير المبيعات حسب نوع الدراجة**: تحليل المبيعات بالرسوم البيانية
- **كشف حساب الضامن**: تفاصيل العملاء المرتبطين بكل ضامن
- **كشف حساب العميل**: تفاصيل فواتير وأقساط كل عميل

## التقنيات المستخدمة

### Backend
- **Python Flask**: إطار العمل الخلفي
- **SQLite**: قاعدة البيانات
- **SQLAlchemy**: للتعامل مع قاعدة البيانات

### Frontend
- **HTML5 & CSS3**: هيكل وتصميم الصفحات
- **Bootstrap 5 RTL**: تصميم متجاوب يدعم العربية
- **JavaScript**: التفاعل والوظائف الديناميكية
- **Chart.js**: الرسوم البيانية
- **Font Awesome**: الأيقونات
- **Google Fonts (Cairo)**: خط عربي جميل

### PWA Support
- **Service Worker**: للعمل بدون إنترنت
- **Web App Manifest**: لتثبيت التطبيق على الهاتف
- **Responsive Design**: متوافق مع جميع أحجام الشاشات

## متطلبات التشغيل

- Python 3.7 أو أحدث
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)

## طريقة التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd koshi
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل النظام
```bash
python run.py
```
أو
```bash
python app.py
```

### 4. فتح النظام
افتح متصفح الويب واذهب إلى:
```
http://localhost:5000
```

## الاستخدام على الهواتف الذكية

### طريقة 1: متصفح الهاتف
1. افتح متصفح الهاتف
2. اذهب إلى عنوان الخادم
3. أضف الموقع إلى الشاشة الرئيسية

### طريقة 2: تثبيت كتطبيق PWA
1. افتح الموقع في Chrome أو Safari
2. اضغط على "إضافة إلى الشاشة الرئيسية"
3. سيعمل التطبيق كتطبيق أصلي

## هيكل المشروع

```
koshi/
├── app.py                 # الخادم الرئيسي
├── database.py           # إعداد قاعدة البيانات
├── run.py               # ملف التشغيل المبسط
├── requirements.txt     # المتطلبات
├── static/
│   ├── css/
│   │   └── style.css   # التصميم الرئيسي
│   ├── js/
│   │   └── main.js     # الوظائف الأساسية
│   ├── manifest.json   # إعدادات PWA
│   └── sw.js          # Service Worker
├── templates/
│   ├── base.html       # القالب الأساسي
│   ├── index.html      # الصفحة الرئيسية
│   ├── customers.html  # إدارة العملاء
│   ├── guarantors.html # إدارة الضامنين
│   ├── invoices.html   # إدارة الفواتير
│   ├── receipts.html   # سندات القبض
│   └── reports.html    # التقارير
└── motorcycle_sales.db  # قاعدة البيانات (تُنشأ تلقائياً)
```

## قاعدة البيانات

يحتوي النظام على الجداول التالية:
- **guarantors**: بيانات الضامنين
- **customers**: بيانات العملاء
- **motorcycle_types**: أنواع الدراجات النارية
- **invoices**: فواتير المبيعات
- **installments**: الأقساط الشهرية
- **receipts**: سندات القبض

## الميزات المتقدمة

### 🔍 البحث والفلترة
- بحث سريع في جميع الجداول
- فلترة متقدمة حسب التاريخ والحالة
- ترتيب النتائج

### 📱 التصميم المتجاوب
- يعمل بشكل مثالي على الهواتف
- تصميم يدعم اللمس
- قوائم منسدلة محسنة للهواتف

### 🖨️ الطباعة والتصدير
- طباعة جميع التقارير والجداول
- تصدير إلى PDF
- تنسيق محسن للطباعة

### 📊 الرسوم البيانية
- رسوم بيانية تفاعلية
- إحصائيات مرئية
- تحليل البيانات

## الدعم والمساعدة

### المشاكل الشائعة

**1. خطأ في تثبيت المتطلبات**
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

**2. مشكلة في الوصول للنظام**
- تأكد من تشغيل الخادم
- تحقق من رقم المنفذ (5000)
- جرب http://127.0.0.1:5000

**3. مشكلة في قاعدة البيانات**
- احذف ملف motorcycle_sales.db
- أعد تشغيل النظام لإنشاء قاعدة بيانات جديدة

## التطوير المستقبلي

- [ ] نظام المستخدمين والصلاحيات
- [ ] إشعارات الأقساط المستحقة
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تطبيق أندرويد أصلي
- [ ] نسخ احتياطية تلقائية
- [ ] تقارير أكثر تفصيلاً

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**تم تطوير هذا النظام باستخدام أحدث التقنيات لضمان الأداء الأمثل والتوافق مع جميع الأجهزة.**
