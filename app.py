from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from database import Database
from config import get_config
from datetime import datetime, date, timedelta
import json

# إنشاء التطبيق مع التكوين
app = Flask(__name__)
config_class = get_config()
app.config.from_object(config_class)
app.secret_key = app.config['SECRET_KEY']

# إنشاء مثيل من قاعدة البيانات
db = Database()


# فلتر مخصص لتنسيق الأرقام
@app.template_filter('format_number')
def format_number(value):
    """تنسيق الأرقام مع فواصل الآلاف"""
    try:
        return "{:,}".format(int(float(value)))
    except (ValueError, TypeError):
        return "0"

@app.template_filter('format_currency')
def format_currency(value):
    """تنسيق الأرقام كعملة"""
    try:
        return "{:,} ر.س".format(int(float(value)))
    except (ValueError, TypeError):
        return "0 ر.س"

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/customers')
def customers():
    """صفحة إدارة العملاء"""
    conn = db.get_connection()
    cursor = conn.cursor()
    
    # جلب جميع العملاء مع بيانات الضامنين
    cursor.execute('''
        SELECT c.*, g.name as guarantor_name 
        FROM customers c 
        LEFT JOIN guarantors g ON c.guarantor_id = g.id
        ORDER BY c.created_at DESC
    ''')
    customers_list = cursor.fetchall()
    
    # جلب جميع الضامنين للقائمة المنسدلة
    cursor.execute('SELECT * FROM guarantors ORDER BY name')
    guarantors_list = cursor.fetchall()
    
    conn.close()
    return render_template('customers.html', customers=customers_list, guarantors=guarantors_list)

@app.route('/guarantors')
def guarantors():
    """صفحة إدارة الضامنين"""
    conn = db.get_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM guarantors ORDER BY created_at DESC')
    guarantors_list = cursor.fetchall()
    conn.close()
    return render_template('guarantors.html', guarantors=guarantors_list)

@app.route('/invoices')
def invoices():
    """صفحة إدارة الفواتير"""
    conn = db.get_connection()
    cursor = conn.cursor()
    
    # جلب الفواتير مع بيانات العملاء والدراجات
    cursor.execute('''
        SELECT i.*, c.name as customer_name, mt.name as motorcycle_name, mt.model
        FROM invoices i
        JOIN customers c ON i.customer_id = c.id
        JOIN motorcycle_types mt ON i.motorcycle_type_id = mt.id
        ORDER BY i.created_at DESC
    ''')
    invoices_list = cursor.fetchall()
    
    # جلب العملاء وأنواع الدراجات للقوائم المنسدلة
    cursor.execute('SELECT * FROM customers ORDER BY name')
    customers_list = cursor.fetchall()
    
    cursor.execute('SELECT * FROM motorcycle_types ORDER BY name')
    motorcycles_list = cursor.fetchall()
    
    conn.close()
    return render_template('invoices.html', 
                         invoices=invoices_list, 
                         customers=customers_list, 
                         motorcycles=motorcycles_list)

@app.route('/receipts')
def receipts():
    """صفحة إدارة سندات القبض"""
    conn = db.get_connection()
    cursor = conn.cursor()
    
    # جلب سندات القبض مع بيانات العملاء
    cursor.execute('''
        SELECT r.*, c.name as customer_name
        FROM receipts r
        JOIN customers c ON r.customer_id = c.id
        ORDER BY r.created_at DESC
    ''')
    receipts_list = cursor.fetchall()
    
    # جلب العملاء للقائمة المنسدلة
    cursor.execute('SELECT * FROM customers ORDER BY name')
    customers_list = cursor.fetchall()
    
    conn.close()
    return render_template('receipts.html', receipts=receipts_list, customers=customers_list)

@app.route('/reports')
def reports():
    """صفحة التقارير"""
    return render_template('reports.html')

# API Routes للعمليات CRUD

@app.route('/api/customers', methods=['POST'])
def add_customer():
    """إضافة عميل جديد"""
    data = request.get_json()
    
    conn = db.get_connection()
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO customers (name, phone, address, national_id, guarantor_id)
        VALUES (?, ?, ?, ?, ?)
    ''', (data['name'], data['phone'], data['address'], 
          data['national_id'], data.get('guarantor_id')))
    
    conn.commit()
    conn.close()
    
    return jsonify({'success': True, 'message': 'تم إضافة العميل بنجاح'})

@app.route('/api/customers/<int:customer_id>', methods=['GET'])
def get_customer(customer_id):
    """جلب بيانات عميل محدد"""
    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT c.*, g.name as guarantor_name
        FROM customers c
        LEFT JOIN guarantors g ON c.guarantor_id = g.id
        WHERE c.id = ?
    ''', (customer_id,))

    customer = cursor.fetchone()
    conn.close()

    if customer:
        return jsonify({
            'success': True,
            'customer': dict(customer)
        })
    else:
        return jsonify({'success': False, 'message': 'العميل غير موجود'})

@app.route('/api/customers/<int:customer_id>', methods=['PUT'])
def update_customer(customer_id):
    """تحديث بيانات عميل"""
    data = request.get_json()

    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        UPDATE customers
        SET name = ?, phone = ?, address = ?, national_id = ?, guarantor_id = ?
        WHERE id = ?
    ''', (data['name'], data['phone'], data['address'],
          data['national_id'], data.get('guarantor_id'), customer_id))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم تحديث بيانات العميل بنجاح'})

@app.route('/api/customers/<int:customer_id>', methods=['DELETE'])
def delete_customer(customer_id):
    """حذف عميل"""
    conn = db.get_connection()
    cursor = conn.cursor()

    # التحقق من وجود فواتير للعميل
    cursor.execute('SELECT COUNT(*) FROM invoices WHERE customer_id = ?', (customer_id,))
    invoice_count = cursor.fetchone()[0]

    if invoice_count > 0:
        conn.close()
        return jsonify({
            'success': False,
            'message': f'لا يمكن حذف العميل لأن لديه {invoice_count} فاتورة مرتبطة'
        })

    cursor.execute('DELETE FROM customers WHERE id = ?', (customer_id,))
    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم حذف العميل بنجاح'})

@app.route('/api/guarantors', methods=['POST'])
def add_guarantor():
    """إضافة ضامن جديد"""
    data = request.get_json()
    
    conn = db.get_connection()
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO guarantors (name, phone, address, national_id)
        VALUES (?, ?, ?, ?)
    ''', (data['name'], data['phone'], data['address'], data['national_id']))
    
    conn.commit()
    conn.close()
    
    return jsonify({'success': True, 'message': 'تم إضافة الضامن بنجاح'})

@app.route('/api/guarantors/<int:guarantor_id>', methods=['GET'])
def get_guarantor(guarantor_id):
    """جلب بيانات ضامن محدد"""
    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('SELECT * FROM guarantors WHERE id = ?', (guarantor_id,))
    guarantor = cursor.fetchone()
    conn.close()

    if guarantor:
        return jsonify({
            'success': True,
            'guarantor': dict(guarantor)
        })
    else:
        return jsonify({'success': False, 'message': 'الضامن غير موجود'})

@app.route('/api/guarantors/<int:guarantor_id>', methods=['PUT'])
def update_guarantor(guarantor_id):
    """تحديث بيانات ضامن"""
    data = request.get_json()

    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        UPDATE guarantors
        SET name = ?, phone = ?, address = ?, national_id = ?
        WHERE id = ?
    ''', (data['name'], data['phone'], data['address'], data['national_id'], guarantor_id))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم تحديث بيانات الضامن بنجاح'})

@app.route('/api/guarantors/<int:guarantor_id>', methods=['DELETE'])
def delete_guarantor(guarantor_id):
    """حذف ضامن"""
    conn = db.get_connection()
    cursor = conn.cursor()

    # التحقق من وجود عملاء مرتبطين بالضامن
    cursor.execute('SELECT COUNT(*) FROM customers WHERE guarantor_id = ?', (guarantor_id,))
    customer_count = cursor.fetchone()[0]

    if customer_count > 0:
        conn.close()
        return jsonify({
            'success': False,
            'message': f'لا يمكن حذف الضامن لأن لديه {customer_count} عميل مرتبط'
        })

    cursor.execute('DELETE FROM guarantors WHERE id = ?', (guarantor_id,))
    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم حذف الضامن بنجاح'})

@app.route('/api/invoices', methods=['POST'])
def add_invoice():
    """إضافة فاتورة جديدة"""
    data = request.get_json()
    
    conn = db.get_connection()
    cursor = conn.cursor()
    
    # إنشاء رقم فاتورة تلقائي
    cursor.execute('SELECT COUNT(*) FROM invoices')
    count = cursor.fetchone()[0]
    invoice_number = f"INV-{count + 1:06d}"
    
    # حساب المبلغ المتبقي
    remaining_amount = data['total_amount'] - data['down_payment']
    
    cursor.execute('''
        INSERT INTO invoices (invoice_number, customer_id, motorcycle_type_id, 
                            total_amount, down_payment, remaining_amount, 
                            installment_amount, installment_count, invoice_date)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (invoice_number, data['customer_id'], data['motorcycle_type_id'],
          data['total_amount'], data['down_payment'], remaining_amount,
          data['installment_amount'], data['installment_count'], data['invoice_date']))
    
    invoice_id = cursor.lastrowid
    
    # إنشاء الأقساط
    invoice_date = datetime.strptime(data['invoice_date'], '%Y-%m-%d').date()
    for i in range(data['installment_count']):
        due_date = invoice_date + timedelta(days=30 * (i + 1))
        cursor.execute('''
            INSERT INTO installments (invoice_id, installment_number, amount, due_date)
            VALUES (?, ?, ?, ?)
        ''', (invoice_id, i + 1, data['installment_amount'], due_date))
    
    conn.commit()
    conn.close()
    
    return jsonify({'success': True, 'message': 'تم إضافة الفاتورة بنجاح'})

@app.route('/api/invoices/<int:invoice_id>', methods=['GET'])
def get_invoice(invoice_id):
    """جلب بيانات فاتورة محددة"""
    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT i.*, c.name as customer_name, c.phone as customer_phone,
               m.name as motorcycle_name, m.model as motorcycle_model,
               g.name as guarantor_name
        FROM invoices i
        JOIN customers c ON i.customer_id = c.id
        JOIN motorcycle_types m ON i.motorcycle_type_id = m.id
        LEFT JOIN guarantors g ON c.guarantor_id = g.id
        WHERE i.id = ?
    ''', (invoice_id,))

    invoice = cursor.fetchone()
    conn.close()

    if invoice:
        return jsonify({
            'success': True,
            'invoice': dict(invoice)
        })
    else:
        return jsonify({'success': False, 'message': 'الفاتورة غير موجودة'})

@app.route('/api/invoices/<int:invoice_id>', methods=['PUT'])
def update_invoice(invoice_id):
    """تحديث بيانات فاتورة"""
    data = request.get_json()

    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        UPDATE invoices
        SET customer_id = ?, motorcycle_type_id = ?, total_amount = ?,
            down_payment = ?, remaining_amount = ?, installment_amount = ?,
            installment_count = ?, invoice_date = ?
        WHERE id = ?
    ''', (data['customer_id'], data['motorcycle_type_id'], data['total_amount'],
          data['down_payment'], data['remaining_amount'], data['installment_amount'],
          data['installment_count'], data['invoice_date'], invoice_id))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم تحديث الفاتورة بنجاح'})

@app.route('/api/invoices/<int:invoice_id>/installments', methods=['GET'])
def get_invoice_installments(invoice_id):
    """جلب أقساط فاتورة محددة"""
    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT * FROM installments
        WHERE invoice_id = ?
        ORDER BY installment_number
    ''', (invoice_id,))

    installments = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return jsonify({
        'success': True,
        'installments': installments
    })

@app.route('/api/receipts', methods=['POST'])
def add_receipt():
    """إضافة سند قبض جديد"""
    data = request.get_json()

    conn = db.get_connection()
    cursor = conn.cursor()

    # إنشاء رقم سند تلقائي
    cursor.execute('SELECT COUNT(*) FROM receipts')
    count = cursor.fetchone()[0]
    receipt_number = f"REC-{count + 1:06d}"

    cursor.execute('''
        INSERT INTO receipts (receipt_number, customer_id, amount, payment_date, payment_method, notes)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', (receipt_number, data['customer_id'], data['amount'],
          data['payment_date'], data['payment_method'], data.get('notes', '')))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم إضافة سند القبض بنجاح'})

@app.route('/api/receipts/<int:receipt_id>', methods=['GET'])
def get_receipt(receipt_id):
    """جلب بيانات سند قبض محدد"""
    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT r.*, c.name as customer_name
        FROM receipts r
        JOIN customers c ON r.customer_id = c.id
        WHERE r.id = ?
    ''', (receipt_id,))

    receipt = cursor.fetchone()
    conn.close()

    if receipt:
        return jsonify({
            'success': True,
            'receipt': dict(receipt)
        })
    else:
        return jsonify({'success': False, 'message': 'سند القبض غير موجود'})

@app.route('/api/receipts/<int:receipt_id>', methods=['PUT'])
def update_receipt(receipt_id):
    """تحديث بيانات سند قبض"""
    data = request.get_json()

    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        UPDATE receipts
        SET customer_id = ?, amount = ?, payment_date = ?, payment_method = ?, notes = ?
        WHERE id = ?
    ''', (data['customer_id'], data['amount'], data['payment_date'],
          data['payment_method'], data.get('notes', ''), receipt_id))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم تحديث سند القبض بنجاح'})

@app.route('/api/receipts/<int:receipt_id>', methods=['DELETE'])
def delete_receipt(receipt_id):
    """حذف سند قبض"""
    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('DELETE FROM receipts WHERE id = ?', (receipt_id,))
    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم حذف سند القبض بنجاح'})

# API للحصول على الإحصائيات
@app.route('/api/stats')
def get_stats():
    """جلب الإحصائيات السريعة"""
    conn = db.get_connection()
    cursor = conn.cursor()

    # عدد العملاء
    cursor.execute('SELECT COUNT(*) FROM customers')
    total_customers = cursor.fetchone()[0]

    # إجمالي المبيعات
    cursor.execute('SELECT COALESCE(SUM(total_amount), 0) FROM invoices')
    total_sales = cursor.fetchone()[0]

    # الأقساط المستحقة
    cursor.execute('SELECT COUNT(*) FROM installments WHERE status = "pending"')
    pending_installments = cursor.fetchone()[0]

    # المتأخرات
    cursor.execute('''
        SELECT COALESCE(SUM(amount), 0) FROM installments
        WHERE status = "pending" AND due_date < date('now')
    ''')
    overdue_amount = cursor.fetchone()[0]

    conn.close()

    return jsonify({
        'total_customers': total_customers,
        'total_sales': total_sales,
        'pending_installments': pending_installments,
        'overdue_amount': overdue_amount
    })

if __name__ == '__main__':
    app.run(
        debug=app.config['DEBUG'],
        host=app.config['HOST'],
        port=app.config['PORT']
    )
