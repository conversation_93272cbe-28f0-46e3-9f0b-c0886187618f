#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تشغيل النسخة العاملة من نظام محاسبة مبيعات الدراجات النارية
"""

import sys
import os

def main():
    print("🏍️ نظام محاسبة مبيعات الدراجات النارية - النسخة العاملة")
    print("=" * 65)
    print("🚀 بدء التشغيل...")
    
    try:
        # اختبار المكتبات المطلوبة
        print("🔍 فحص المتطلبات...")
        
        import tkinter as tk
        print("✅ tkinter متوفر")
        
        import sqlite3
        print("✅ sqlite3 متوفر")
        
        # تشغيل التطبيق
        print("✅ جميع المتطلبات متوفرة")
        print("🚀 تشغيل التطبيق...")
        print("💡 ستظهر النافذة الرئيسية خلال ثوانٍ...")
        print("-" * 65)
        
        from working_app import main as app_main
        app_main()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من تثبيت Python بشكل صحيح مع tkinter")
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        print("تفاصيل الخطأ:")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 65)
    print("🙏 شكراً لاستخدام نظام محاسبة مبيعات الدراجات النارية")
    print("=" * 65)
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
