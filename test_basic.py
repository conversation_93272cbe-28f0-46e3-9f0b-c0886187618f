#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار أساسي للتطبيق
"""

import tkinter as tk
from tkinter import messagebox
import sqlite3

def test_basic():
    """اختبار أساسي"""
    print("🧪 اختبار أساسي للنظام")
    print("=" * 40)
    
    # اختبار tkinter
    try:
        root = tk.Tk()
        root.title("🏍️ اختبار النظام")
        root.geometry("600x400")
        
        # إضافة عنوان
        label = tk.Label(
            root, 
            text="🏍️ نظام محاسبة مبيعات الدراجات النارية\n\nالاختبار الأساسي",
            font=("Arial", 16),
            pady=50
        )
        label.pack()
        
        # إضافة زر
        def show_message():
            messagebox.showinfo("نجح!", "✅ النظام يعمل بشكل صحيح!")
        
        button = tk.Button(
            root,
            text="🧪 اختبار النظام",
            command=show_message,
            font=("Arial", 12),
            bg="#27ae60",
            fg="white",
            padx=20,
            pady=10
        )
        button.pack(pady=20)
        
        # اختبار قاعدة البيانات
        def test_database():
            try:
                conn = sqlite3.connect(":memory:")
                cursor = conn.cursor()
                cursor.execute("CREATE TABLE test (id INTEGER, name TEXT)")
                cursor.execute("INSERT INTO test VALUES (1, 'اختبار')")
                cursor.execute("SELECT * FROM test")
                result = cursor.fetchone()
                conn.close()
                
                if result:
                    messagebox.showinfo("قاعدة البيانات", "✅ قاعدة البيانات تعمل بشكل صحيح!")
                else:
                    messagebox.showerror("قاعدة البيانات", "❌ مشكلة في قاعدة البيانات")
            except Exception as e:
                messagebox.showerror("خطأ", f"❌ خطأ في قاعدة البيانات: {e}")
        
        db_button = tk.Button(
            root,
            text="🗄️ اختبار قاعدة البيانات",
            command=test_database,
            font=("Arial", 12),
            bg="#3498db",
            fg="white",
            padx=20,
            pady=10
        )
        db_button.pack(pady=10)
        
        # زر الخروج
        exit_button = tk.Button(
            root,
            text="❌ خروج",
            command=root.destroy,
            font=("Arial", 12),
            bg="#e74c3c",
            fg="white",
            padx=20,
            pady=10
        )
        exit_button.pack(pady=20)
        
        print("✅ تم إنشاء النافذة بنجاح")
        print("💡 ستظهر نافذة الاختبار...")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic()
