import sqlite3
from datetime import datetime, date
import os

class Database:
    def __init__(self, db_name='motorcycle_sales.db'):
        self.db_name = db_name
        self.init_database()
    
    def get_connection(self):
        conn = sqlite3.connect(self.db_name)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول الضامنين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS guarantors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                national_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                national_id TEXT,
                guarantor_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guarantor_id) REFERENCES guarantors (id)
            )
        ''')
        
        # جدول أنواع الدراجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS motorcycle_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                model TEXT,
                price REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                motorcycle_type_id INTEGER NOT NULL,
                total_amount REAL NOT NULL,
                down_payment REAL DEFAULT 0,
                remaining_amount REAL NOT NULL,
                installment_amount REAL NOT NULL,
                installment_count INTEGER NOT NULL,
                invoice_date DATE NOT NULL,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (motorcycle_type_id) REFERENCES motorcycle_types (id)
            )
        ''')
        
        # جدول الأقساط
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS installments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                installment_number INTEGER NOT NULL,
                amount REAL NOT NULL,
                due_date DATE NOT NULL,
                paid_date DATE,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id)
            )
        ''')
        
        # جدول سندات القبض
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS receipts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                receipt_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                invoice_id INTEGER,
                amount REAL NOT NULL,
                payment_date DATE NOT NULL,
                payment_method TEXT DEFAULT 'cash',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (invoice_id) REFERENCES invoices (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # إدراج بيانات تجريبية
        self.insert_sample_data()
    
    def insert_sample_data(self):
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM motorcycle_types")
        if cursor.fetchone()[0] == 0:
            # إدراج أنواع الدراجات
            motorcycle_types = [
                ('هوندا سي بي 150', '2024', 45000),
                ('ياماها إف زد 150', '2024', 48000),
                ('سوزوكي جي إس 125', '2024', 42000),
                ('كاواساكي نينجا 250', '2024', 65000),
                ('هوندا سي آر إف 250', '2024', 58000)
            ]
            
            cursor.executemany(
                "INSERT INTO motorcycle_types (name, model, price) VALUES (?, ?, ?)",
                motorcycle_types
            )
        
        conn.commit()
        conn.close()

# إنشاء مثيل من قاعدة البيانات
db = Database()
