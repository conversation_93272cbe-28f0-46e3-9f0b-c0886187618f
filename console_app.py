#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام محاسبة مبيعات الدراجات النارية - نسخة وحدة التحكم
"""

import sqlite3
from datetime import date

class ConsoleMotorcycleApp:
    def __init__(self):
        self.db_path = "motorcycle_accounting.db"
        self.setup_database()
        self.insert_sample_data()
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                motorcycle_type TEXT NOT NULL,
                total_amount REAL NOT NULL,
                down_payment REAL DEFAULT 0,
                remaining_amount REAL NOT NULL,
                invoice_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ تم إعداد قاعدة البيانات بنجاح")
    
    def insert_sample_data(self):
        """إدراج بيانات تجريبية"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM customers")
        if cursor.fetchone()[0] == 0:
            # إدراج عملاء تجريبيين
            customers = [
                ("محمد أحمد العميل", "0501111111", "الرياض - حي النخيل"),
                ("سارة محمد العميل", "0502222222", "جدة - حي الصفا"),
                ("عبدالله سعد العميل", "0503333333", "الدمام - حي الفيصلية")
            ]
            cursor.executemany(
                "INSERT INTO customers (name, phone, address) VALUES (?, ?, ?)",
                customers
            )
            
            # إدراج فواتير تجريبية
            invoices = [
                ("INV-000001", 1, "BMW - S1000RR", 85000, 15000, 70000, "2024-01-15"),
                ("INV-000002", 2, "هوندا - CBR 150", 25000, 5000, 20000, "2024-01-16"),
                ("INV-000003", 3, "ياماها - YZF-R15", 28000, 8000, 20000, "2024-01-17")
            ]
            cursor.executemany(
                "INSERT INTO invoices (invoice_number, customer_id, motorcycle_type, total_amount, down_payment, remaining_amount, invoice_date) VALUES (?, ?, ?, ?, ?, ?, ?)",
                invoices
            )
            
            conn.commit()
            print("✅ تم إدراج البيانات التجريبية")
        
        conn.close()
    
    def show_menu(self):
        """عرض القائمة الرئيسية"""
        print("\n" + "=" * 60)
        print("🏍️  نظام محاسبة مبيعات الدراجات النارية")
        print("=" * 60)
        print("1. 👥 عرض العملاء")
        print("2. ➕ إضافة عميل جديد")
        print("3. 📄 عرض الفواتير")
        print("4. ➕ إنشاء فاتورة جديدة")
        print("5. 📊 عرض الإحصائيات")
        print("0. ❌ خروج")
        print("-" * 60)
    
    def show_customers(self):
        """عرض العملاء"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name, phone, address FROM customers ORDER BY created_at DESC")
        customers = cursor.fetchall()
        
        print("\n👥 قائمة العملاء:")
        print("-" * 80)
        print(f"{'ID':<5} {'الاسم':<25} {'الهاتف':<15} {'العنوان':<30}")
        print("-" * 80)
        
        for customer in customers:
            print(f"{customer[0]:<5} {customer[1]:<25} {customer[2] or 'غير محدد':<15} {customer[3] or 'غير محدد':<30}")
        
        print(f"\nإجمالي العملاء: {len(customers)}")
        conn.close()
    
    def add_customer(self):
        """إضافة عميل جديد"""
        print("\n➕ إضافة عميل جديد:")
        print("-" * 30)
        
        name = input("الاسم (مطلوب): ").strip()
        if not name:
            print("❌ الاسم مطلوب!")
            return
        
        phone = input("رقم الهاتف: ").strip()
        address = input("العنوان: ").strip()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            "INSERT INTO customers (name, phone, address) VALUES (?, ?, ?)",
            (name, phone, address)
        )
        
        conn.commit()
        conn.close()
        
        print("✅ تم إضافة العميل بنجاح!")
    
    def show_invoices(self):
        """عرض الفواتير"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT i.id, i.invoice_number, c.name, i.motorcycle_type, 
                   i.total_amount, i.down_payment, i.remaining_amount, i.invoice_date
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            ORDER BY i.created_at DESC
        ''')
        
        invoices = cursor.fetchall()
        
        print("\n📄 قائمة الفواتير:")
        print("-" * 120)
        print(f"{'ID':<5} {'رقم الفاتورة':<12} {'العميل':<20} {'نوع الدراجة':<20} {'المبلغ':<10} {'المقدم':<10} {'المتبقي':<10} {'التاريخ':<12}")
        print("-" * 120)
        
        for invoice in invoices:
            print(f"{invoice[0]:<5} {invoice[1]:<12} {invoice[2]:<20} {invoice[3]:<20} {invoice[4]:<10.0f} {invoice[5]:<10.0f} {invoice[6]:<10.0f} {invoice[7]:<12}")
        
        print(f"\nإجمالي الفواتير: {len(invoices)}")
        conn.close()
    
    def add_invoice(self):
        """إنشاء فاتورة جديدة"""
        print("\n➕ إنشاء فاتورة جديدة:")
        print("-" * 35)
        
        # عرض العملاء المتاحين
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM customers ORDER BY name")
        customers = cursor.fetchall()
        
        if not customers:
            print("❌ لا يوجد عملاء! يرجى إضافة عميل أولاً.")
            conn.close()
            return
        
        print("\nالعملاء المتاحين:")
        for customer in customers:
            print(f"{customer[0]}. {customer[1]}")
        
        try:
            customer_id = int(input("\nاختر رقم العميل: "))
            if customer_id not in [c[0] for c in customers]:
                print("❌ رقم العميل غير صحيح!")
                conn.close()
                return
        except ValueError:
            print("❌ يرجى إدخال رقم صحيح!")
            conn.close()
            return
        
        motorcycle_type = input("نوع الدراجة (مثال: BMW - S1000RR): ").strip()
        if not motorcycle_type:
            print("❌ نوع الدراجة مطلوب!")
            conn.close()
            return
        
        try:
            total_amount = float(input("المبلغ الإجمالي: "))
            down_payment = float(input("المقدم (اتركه فارغ للصفر): ") or "0")
        except ValueError:
            print("❌ يرجى إدخال قيم رقمية صحيحة!")
            conn.close()
            return
        
        remaining_amount = total_amount - down_payment
        
        # إنشاء رقم فاتورة
        cursor.execute('SELECT COUNT(*) FROM invoices')
        count = cursor.fetchone()[0]
        invoice_number = f"INV-{count + 1:06d}"
        
        cursor.execute('''
            INSERT INTO invoices (invoice_number, customer_id, motorcycle_type, 
                                total_amount, down_payment, remaining_amount, invoice_date)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            invoice_number,
            customer_id,
            motorcycle_type,
            total_amount,
            down_payment,
            remaining_amount,
            date.today().strftime("%Y-%m-%d")
        ))
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم إنشاء الفاتورة {invoice_number} بنجاح!")
    
    def show_statistics(self):
        """عرض الإحصائيات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # عدد العملاء
        cursor.execute("SELECT COUNT(*) FROM customers")
        total_customers = cursor.fetchone()[0]
        
        # عدد الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoices")
        total_invoices = cursor.fetchone()[0]
        
        # إجمالي المبيعات
        cursor.execute("SELECT COALESCE(SUM(total_amount), 0) FROM invoices")
        total_sales = cursor.fetchone()[0]
        
        # إجمالي المقدمات
        cursor.execute("SELECT COALESCE(SUM(down_payment), 0) FROM invoices")
        total_down_payments = cursor.fetchone()[0]
        
        # إجمالي المتبقي
        cursor.execute("SELECT COALESCE(SUM(remaining_amount), 0) FROM invoices")
        total_remaining = cursor.fetchone()[0]
        
        conn.close()
        
        print("\n📊 إحصائيات النظام:")
        print("=" * 40)
        print(f"👥 إجمالي العملاء: {total_customers}")
        print(f"📄 إجمالي الفواتير: {total_invoices}")
        print(f"💰 إجمالي المبيعات: {total_sales:,.0f} ر.س")
        print(f"💵 إجمالي المقدمات: {total_down_payments:,.0f} ر.س")
        print(f"⏰ إجمالي المتبقي: {total_remaining:,.0f} ر.س")
        print("=" * 40)
    
    def run(self):
        """تشغيل التطبيق"""
        print("🚀 مرحباً بك في نظام محاسبة مبيعات الدراجات النارية!")
        
        while True:
            self.show_menu()
            
            try:
                choice = input("اختر رقم العملية: ").strip()
                
                if choice == "1":
                    self.show_customers()
                elif choice == "2":
                    self.add_customer()
                elif choice == "3":
                    self.show_invoices()
                elif choice == "4":
                    self.add_invoice()
                elif choice == "5":
                    self.show_statistics()
                elif choice == "0":
                    print("\n🙏 شكراً لاستخدام النظام!")
                    break
                else:
                    print("❌ اختيار غير صحيح! يرجى المحاولة مرة أخرى.")
                
                input("\nاضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n\n🙏 شكراً لاستخدام النظام!")
                break
            except Exception as e:
                print(f"❌ حدث خطأ: {e}")
                input("اضغط Enter للمتابعة...")

def main():
    """الوظيفة الرئيسية"""
    try:
        app = ConsoleMotorcycleApp()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
