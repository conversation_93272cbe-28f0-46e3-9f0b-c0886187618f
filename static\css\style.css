/* تخصيص الخطوط العربية */
* {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تحسين الخط العربي */
body {
    font-size: 16px;
    line-height: 1.6;
    background-color: #f8f9fa;
}

/* تخصيص شريط التنقل */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

/* تصميم البطاقات السريعة */
.quick-access-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 1rem;
}

.quick-access-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

.icon-wrapper {
    padding: 1rem;
    border-radius: 50%;
    background-color: rgba(0, 123, 255, 0.1);
    display: inline-block;
}

/* بطاقات الإحصائيات */
.stats-card {
    border: none;
    border-radius: 1rem;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: scale(1.05);
}

/* تصميم الجداول */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background-color: #495057;
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table tbody td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* تصميم الأزرار */
.btn {
    font-weight: 500;
    border-radius: 0.5rem;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-group .btn {
    margin: 0 2px;
}

/* تصميم النماذج */
.form-control, .form-select {
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* تصميم المودال */
.modal-content {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
    border-radius: 1rem 1rem 0 0;
}

.modal-title {
    font-weight: 700;
    color: #495057;
}

/* تصميم التنبيهات */
.alert {
    border: none;
    border-radius: 0.75rem;
    font-weight: 500;
}

/* تصميم شاشة التحميل */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* تصميم البحث */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-right: 3rem;
}

.search-box .search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* تصميم الرسوم البيانية */
.chart-container {
    position: relative;
    height: 400px;
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* تصميم الفلاتر */
.filter-section {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* تصميم البطاقات المعلوماتية */
.info-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 1rem;
}

/* تصميم الحالات */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.status-active { background-color: #d4edda; color: #155724; }
.status-pending { background-color: #fff3cd; color: #856404; }
.status-overdue { background-color: #f8d7da; color: #721c24; }
.status-paid { background-color: #d1ecf1; color: #0c5460; }

/* تحسينات للهواتف المحمولة */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn-group .btn {
        margin: 0;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .quick-access-card .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .stats-card h2 {
        font-size: 1.5rem;
    }
    
    .chart-container {
        height: 300px;
        padding: 1rem;
    }
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* تحسين الطباعة */
@media print {
    .navbar, .btn, .modal, .loading-spinner {
        display: none !important;
    }
    
    .container-fluid {
        padding: 0;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
}
