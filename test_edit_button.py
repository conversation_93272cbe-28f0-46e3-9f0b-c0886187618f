#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار زر التعديل في بيانات العملاء
"""

import requests
import json

def test_customer_api():
    """اختبار API العملاء"""
    base_url = "http://localhost:5000"
    
    print("🧪 اختبار API العملاء...")
    print("=" * 50)
    
    # 1. اختبار إضافة عميل جديد
    print("1️⃣ اختبار إضافة عميل جديد...")
    
    new_customer = {
        "name": "عميل تجريبي للاختبار",
        "phone": "0501234567",
        "address": "الرياض، حي التجربة",
        "national_id": "1234567890",
        "guarantor_id": ""
    }
    
    try:
        response = requests.post(f"{base_url}/api/customers", json=new_customer)
        print(f"   حالة الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   النتيجة: {result}")
            if result.get('success'):
                print("   ✅ تم إضافة العميل بنجاح")
            else:
                print("   ❌ فشل في إضافة العميل")
        else:
            print(f"   ❌ خطأ HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    print()
    
    # 2. اختبار جلب قائمة العملاء
    print("2️⃣ اختبار جلب قائمة العملاء...")
    
    try:
        response = requests.get(f"{base_url}/customers")
        print(f"   حالة الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ تم جلب صفحة العملاء بنجاح")
        else:
            print(f"   ❌ خطأ HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    print()
    
    # 3. اختبار جلب بيانات عميل محدد (ID = 1)
    print("3️⃣ اختبار جلب بيانات عميل محدد...")
    
    try:
        response = requests.get(f"{base_url}/api/customers/1")
        print(f"   حالة الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   النتيجة: {result}")
            if result.get('success'):
                print("   ✅ تم جلب بيانات العميل بنجاح")
                customer = result.get('customer', {})
                print(f"   اسم العميل: {customer.get('name', 'غير محدد')}")
            else:
                print("   ❌ فشل في جلب بيانات العميل")
        else:
            print(f"   ❌ خطأ HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    print()
    
    # 4. اختبار تحديث بيانات العميل
    print("4️⃣ اختبار تحديث بيانات العميل...")
    
    updated_customer = {
        "name": "عميل محدث للاختبار",
        "phone": "0507654321",
        "address": "الرياض، حي التحديث",
        "national_id": "0987654321",
        "guarantor_id": ""
    }
    
    try:
        response = requests.put(f"{base_url}/api/customers/1", json=updated_customer)
        print(f"   حالة الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   النتيجة: {result}")
            if result.get('success'):
                print("   ✅ تم تحديث بيانات العميل بنجاح")
            else:
                print("   ❌ فشل في تحديث بيانات العميل")
        else:
            print(f"   ❌ خطأ HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    print()
    print("=" * 50)
    print("🏁 انتهى الاختبار")

def check_server_status():
    """التحقق من حالة الخادم"""
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        if response.status_code == 200:
            print("✅ الخادم يعمل بشكل طبيعي")
            return True
        else:
            print(f"⚠️ الخادم يستجيب بحالة: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم")
        print("💡 تأكد من تشغيل الخادم باستخدام: python run.py")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print("🧪 اختبار زر التعديل في بيانات العملاء")
    print("=" * 60)
    print()
    
    # التحقق من حالة الخادم
    print("🔍 التحقق من حالة الخادم...")
    if not check_server_status():
        return
    
    print()
    
    # تشغيل اختبارات API
    test_customer_api()
    
    print()
    print("📋 تعليمات الاختبار اليدوي:")
    print("1. افتح المتصفح واذهب إلى: http://localhost:5000")
    print("2. اذهب إلى صفحة 'إدارة العملاء'")
    print("3. اضغط على زر 'تعديل' لأي عميل")
    print("4. تأكد من ظهور بيانات العميل في النموذج")
    print("5. قم بتعديل البيانات واضغط 'حفظ التعديلات'")
    print("6. تأكد من حفظ التعديلات وإعادة تحميل الصفحة")

if __name__ == '__main__':
    main()
