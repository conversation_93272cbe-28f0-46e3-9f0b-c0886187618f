#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ملف التكوين الرئيسي لنظام محاسبة مبيعات الدراجات النارية
"""

import os
from datetime import timedelta

class Config:
    """إعدادات النظام الأساسية"""
    
    # إعدادات Flask
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'motorcycle-sales-secret-key-2024'
    DEBUG = True
    
    # إعدادات قاعدة البيانات
    DATABASE_NAME = 'motorcycle_sales.db'
    DATABASE_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), DATABASE_NAME)
    
    # إعدادات الخادم
    HOST = '0.0.0.0'
    PORT = 5000
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # إعدادات التطبيق
    APP_NAME = 'نظام محاسبة مبيعات الدراجات النارية'
    APP_VERSION = '1.0.0'
    APP_AUTHOR = 'فريق التطوير'
    
    # إعدادات العملة
    CURRENCY = 'ر.س'
    CURRENCY_SYMBOL = 'SAR'
    
    # إعدادات التقارير
    REPORTS_PER_PAGE = 50
    MAX_EXPORT_RECORDS = 1000
    
    # إعدادات الأقساط
    DEFAULT_INSTALLMENT_PERIODS = [6, 12, 18, 24, 36]
    OVERDUE_DAYS_THRESHOLD = 30
    
    # إعدادات الإشعارات
    REMINDER_DAYS_BEFORE_DUE = 7
    
    # إعدادات الملفات
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif'}
    
    # إعدادات النسخ الاحتياطي
    BACKUP_FOLDER = 'backups'
    AUTO_BACKUP_ENABLED = True
    BACKUP_RETENTION_DAYS = 30

class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    DEBUG = False
    TESTING = False
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'change-this-in-production'

class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    TESTING = True
    DATABASE_NAME = 'test_motorcycle_sales.db'

# تحديد التكوين المستخدم
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """الحصول على التكوين المناسب"""
    env = os.environ.get('FLASK_ENV', 'default')
    return config.get(env, config['default'])

# إعدادات إضافية للنظام
MOTORCYCLE_TYPES = [
    {'name': 'هوندا سي بي 150', 'model': '2024', 'price': 45000},
    {'name': 'ياماها إف زد 150', 'model': '2024', 'price': 48000},
    {'name': 'سوزوكي جي إس 125', 'model': '2024', 'price': 42000},
    {'name': 'كاواساكي نينجا 250', 'model': '2024', 'price': 65000},
    {'name': 'هوندا سي آر إف 250', 'model': '2024', 'price': 58000},
    {'name': 'ياماها آر 15', 'model': '2024', 'price': 52000},
    {'name': 'كي تي إم ديوك 200', 'model': '2024', 'price': 55000},
]

PAYMENT_METHODS = [
    {'value': 'cash', 'label': 'نقداً'},
    {'value': 'bank_transfer', 'label': 'تحويل بنكي'},
    {'value': 'check', 'label': 'شيك'},
    {'value': 'credit_card', 'label': 'بطاقة ائتمان'},
    {'value': 'installment', 'label': 'قسط شهري'},
]

INVOICE_STATUSES = [
    {'value': 'active', 'label': 'نشطة', 'color': 'success'},
    {'value': 'completed', 'label': 'مكتملة', 'color': 'info'},
    {'value': 'cancelled', 'label': 'ملغية', 'color': 'danger'},
    {'value': 'on_hold', 'label': 'معلقة', 'color': 'warning'},
]

INSTALLMENT_STATUSES = [
    {'value': 'pending', 'label': 'مستحق', 'color': 'warning'},
    {'value': 'paid', 'label': 'مدفوع', 'color': 'success'},
    {'value': 'overdue', 'label': 'متأخر', 'color': 'danger'},
    {'value': 'partial', 'label': 'جزئي', 'color': 'info'},
]

# رسائل النظام
MESSAGES = {
    'success': {
        'customer_added': 'تم إضافة العميل بنجاح',
        'customer_updated': 'تم تحديث بيانات العميل بنجاح',
        'customer_deleted': 'تم حذف العميل بنجاح',
        'guarantor_added': 'تم إضافة الضامن بنجاح',
        'guarantor_updated': 'تم تحديث بيانات الضامن بنجاح',
        'guarantor_deleted': 'تم حذف الضامن بنجاح',
        'invoice_added': 'تم إنشاء الفاتورة بنجاح',
        'invoice_updated': 'تم تحديث الفاتورة بنجاح',
        'receipt_added': 'تم إضافة سند القبض بنجاح',
        'payment_recorded': 'تم تسجيل الدفعة بنجاح',
        'backup_created': 'تم إنشاء النسخة الاحتياطية بنجاح',
    },
    'error': {
        'customer_not_found': 'العميل غير موجود',
        'guarantor_not_found': 'الضامن غير موجود',
        'invoice_not_found': 'الفاتورة غير موجودة',
        'invalid_data': 'البيانات المدخلة غير صحيحة',
        'database_error': 'خطأ في قاعدة البيانات',
        'permission_denied': 'ليس لديك صلاحية لهذا الإجراء',
        'file_upload_error': 'خطأ في رفع الملف',
    },
    'warning': {
        'overdue_payment': 'يوجد أقساط متأخرة',
        'missing_data': 'بعض البيانات مفقودة',
        'backup_old': 'النسخة الاحتياطية قديمة',
    }
}

# إعدادات التصدير
EXPORT_FORMATS = {
    'pdf': {
        'name': 'PDF',
        'mime_type': 'application/pdf',
        'extension': '.pdf'
    },
    'excel': {
        'name': 'Excel',
        'mime_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'extension': '.xlsx'
    },
    'csv': {
        'name': 'CSV',
        'mime_type': 'text/csv',
        'extension': '.csv'
    }
}

# إعدادات الأمان
SECURITY_SETTINGS = {
    'max_login_attempts': 5,
    'lockout_duration': 30,  # بالدقائق
    'password_min_length': 8,
    'session_timeout': 24,  # بالساعات
    'require_https': False,  # تفعيل في الإنتاج
}
