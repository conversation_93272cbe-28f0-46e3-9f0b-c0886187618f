#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار تطبيق سطح المكتب
"""

import sys
import os

def test_imports():
    """اختبار استيراد الوحدات"""
    print("🧪 اختبار استيراد الوحدات...")
    
    try:
        import tkinter as tk
        print("✅ tkinter متوفر")
    except ImportError:
        print("❌ tkinter غير متوفر")
        return False
    
    try:
        import sqlite3
        print("✅ sqlite3 متوفر")
    except ImportError:
        print("❌ sqlite3 غير متوفر")
        return False
    
    try:
        from dialogs import CustomerDialog, InvoiceDialog
        print("✅ dialogs متوفر")
    except ImportError as e:
        print(f"❌ dialogs غير متوفر: {e}")
        return False
    
    try:
        from desktop_app import MotorcycleAccountingApp
        print("✅ desktop_app متوفر")
    except ImportError as e:
        print(f"❌ desktop_app غير متوفر: {e}")
        return False
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        import sqlite3
        
        # إنشاء قاعدة بيانات تجريبية
        conn = sqlite3.connect(":memory:")
        cursor = conn.cursor()
        
        # إنشاء جدول تجريبي
        cursor.execute('''
            CREATE TABLE test_table (
                id INTEGER PRIMARY KEY,
                name TEXT
            )
        ''')
        
        # إدراج بيانات تجريبية
        cursor.execute("INSERT INTO test_table (name) VALUES (?)", ("اختبار",))
        
        # قراءة البيانات
        cursor.execute("SELECT * FROM test_table")
        result = cursor.fetchone()
        
        conn.close()
        
        if result and result[1] == "اختبار":
            print("✅ قاعدة البيانات تعمل بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_gui():
    """اختبار الواجهة الرسومية"""
    print("\n🖥️ اختبار الواجهة الرسومية...")
    
    try:
        import tkinter as tk
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.title("اختبار")
        root.geometry("300x200")
        
        # إضافة نص عربي
        label = tk.Label(root, text="مرحباً بك في نظام محاسبة الدراجات النارية")
        label.pack(pady=50)
        
        # إغلاق النافذة بعد ثانية واحدة
        root.after(1000, root.destroy)
        
        print("✅ الواجهة الرسومية تعمل")
        print("💡 ستظهر نافذة اختبار لثانية واحدة...")
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الواجهة الرسومية: {e}")
        return False

def test_app_creation():
    """اختبار إنشاء التطبيق"""
    print("\n🚀 اختبار إنشاء التطبيق...")
    
    try:
        import tkinter as tk
        from desktop_app import MotorcycleAccountingApp
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        
        # إنشاء التطبيق
        app = MotorcycleAccountingApp(root)
        
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # إغلاق التطبيق بعد ثانيتين
        root.after(2000, root.destroy)
        
        print("💡 ستظهر النافذة الرئيسية لثانيتين...")
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الوظيفة الرئيسية للاختبار"""
    print("🧪 اختبار تطبيق سطح المكتب")
    print("=" * 50)
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("قاعدة البيانات", test_database),
        ("الواجهة الرسومية", test_gui),
        ("إنشاء التطبيق", test_app_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}: نجح")
        else:
            print(f"❌ {test_name}: فشل")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام")
        print("\n🚀 لتشغيل التطبيق:")
        print("python run_desktop.py")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
