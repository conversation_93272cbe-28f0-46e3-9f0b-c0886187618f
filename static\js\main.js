// الوظائف الأساسية للنظام

// إظهار/إخفاء شاشة التحميل
function showLoading() {
    document.getElementById('loadingSpinner').classList.remove('d-none');
}

function hideLoading() {
    document.getElementById('loadingSpinner').classList.add('d-none');
}

// إظهار رسائل التنبيه
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('main .container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تنسيق الأرقام العربية
function formatArabicNumber(number) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(number);
}

// تنسيق التاريخ العربي
function formatArabicDate(dateString) {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}

// البحث في الجداول
function searchTable(inputId, tableId) {
    const input = document.getElementById(inputId);
    const table = document.getElementById(tableId);
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    
    input.addEventListener('keyup', function() {
        const filter = this.value.toLowerCase();
        
        for (let i = 0; i < rows.length; i++) {
            const cells = rows[i].getElementsByTagName('td');
            let found = false;
            
            for (let j = 0; j < cells.length; j++) {
                if (cells[j].textContent.toLowerCase().indexOf(filter) > -1) {
                    found = true;
                    break;
                }
            }
            
            rows[i].style.display = found ? '' : 'none';
        }
    });
}

// إرسال البيانات عبر AJAX
async function sendData(url, data, method = 'POST') {
    showLoading();
    
    try {
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert(result.message, 'success');
            return result;
        } else {
            showAlert(result.message || 'حدث خطأ غير متوقع', 'danger');
            return null;
        }
    } catch (error) {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
        return null;
    } finally {
        hideLoading();
    }
}

// تصدير الجدول إلى PDF
function exportTableToPDF(tableId, filename = 'تقرير') {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();
    
    // إضافة الخط العربي (يحتاج إلى ملف خط منفصل)
    doc.setFont('Arial', 'normal');
    doc.setFontSize(16);
    
    // عنوان التقرير
    doc.text(filename, 105, 20, { align: 'center' });
    
    // الحصول على بيانات الجدول
    const table = document.getElementById(tableId);
    const rows = table.getElementsByTagName('tr');
    
    let yPosition = 40;
    
    for (let i = 0; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName(i === 0 ? 'th' : 'td');
        let xPosition = 20;
        
        for (let j = 0; j < cells.length; j++) {
            const cellText = cells[j].textContent.trim();
            doc.text(cellText, xPosition, yPosition);
            xPosition += 40;
        }
        
        yPosition += 10;
        
        // إضافة صفحة جديدة إذا لزم الأمر
        if (yPosition > 280) {
            doc.addPage();
            yPosition = 20;
        }
    }
    
    // حفظ الملف
    doc.save(`${filename}.pdf`);
}

// طباعة الجدول
function printTable(tableId) {
    const table = document.getElementById(tableId);
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>طباعة التقرير</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f2f2f2; font-weight: bold; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <h2 style="text-align: center;">تقرير النظام</h2>
            ${table.outerHTML}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
}

// التحقق من صحة النماذج
function validateForm(formId) {
    const form = document.getElementById(formId);
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

// تنظيف النماذج
function clearForm(formId) {
    const form = document.getElementById(formId);
    form.reset();
    
    // إزالة رسائل الخطأ
    const invalidInputs = form.querySelectorAll('.is-invalid');
    invalidInputs.forEach(input => {
        input.classList.remove('is-invalid');
    });
}

// تحديث الوقت الحالي
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// تشغيل تحديث الوقت كل دقيقة
setInterval(updateCurrentTime, 60000);

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الوقت الحالي
    updateCurrentTime();
    
    // إضافة تأثيرات الحركة للعناصر
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
    
    // تفعيل tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

// معالجة الأخطاء العامة
window.addEventListener('error', function(e) {
    console.error('خطأ في JavaScript:', e.error);
    showAlert('حدث خطأ في النظام. يرجى إعادة تحميل الصفحة.', 'danger');
});

// دعم PWA - تثبيت التطبيق
let deferredPrompt;

window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
    
    // إظهار زر التثبيت
    const installButton = document.getElementById('installButton');
    if (installButton) {
        installButton.style.display = 'block';
        
        installButton.addEventListener('click', () => {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    console.log('تم تثبيت التطبيق');
                }
                deferredPrompt = null;
            });
        });
    }
});

// إشعار عند تثبيت التطبيق
window.addEventListener('appinstalled', (evt) => {
    showAlert('تم تثبيت التطبيق بنجاح!', 'success');
});
