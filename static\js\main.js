// الوظائف الأساسية للنظام

// إظهار/إخفاء شاشة التحميل
function showLoading() {
    document.getElementById('loadingSpinner').classList.remove('d-none');
}

function hideLoading() {
    document.getElementById('loadingSpinner').classList.add('d-none');
}

// إظهار رسائل التنبيه
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('main .container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تنسيق الأرقام العربية
function formatArabicNumber(number) {
    if (isNaN(number) || number === null || number === undefined) {
        return '0 ر.س';
    }

    // تحويل الرقم إلى نص مع فواصل الآلاف
    const formattedNumber = Number(number).toLocaleString('ar-SA');
    return formattedNumber + ' ر.س';
}

// تنسيق الأرقام بدون عملة
function formatNumber(number) {
    if (isNaN(number) || number === null || number === undefined) {
        return '0';
    }
    return Number(number).toLocaleString('ar-SA');
}

// تنسيق التاريخ العربي
function formatArabicDate(dateString) {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}

// البحث في الجداول
function searchTable(inputId, tableId) {
    const input = document.getElementById(inputId);
    const table = document.getElementById(tableId);
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    
    input.addEventListener('keyup', function() {
        const filter = this.value.toLowerCase();
        
        for (let i = 0; i < rows.length; i++) {
            const cells = rows[i].getElementsByTagName('td');
            let found = false;
            
            for (let j = 0; j < cells.length; j++) {
                if (cells[j].textContent.toLowerCase().indexOf(filter) > -1) {
                    found = true;
                    break;
                }
            }
            
            rows[i].style.display = found ? '' : 'none';
        }
    });
}

// إرسال البيانات عبر AJAX
async function sendData(url, data, method = 'POST') {
    showLoading();
    
    try {
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert(result.message, 'success');
            return result;
        } else {
            showAlert(result.message || 'حدث خطأ غير متوقع', 'danger');
            return null;
        }
    } catch (error) {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
        return null;
    } finally {
        hideLoading();
    }
}

// تصدير الجدول إلى PDF مع دعم العربية
function exportTableToPDF(tableId, filename = 'تقرير') {
    try {
        // استخدام html2canvas لتحويل الجدول إلى صورة
        const table = document.getElementById(tableId);
        if (!table) {
            showAlert('الجدول غير موجود', 'error');
            return;
        }

        // إنشاء نسخة من الجدول للطباعة
        const printTable = table.cloneNode(true);
        printTable.style.fontSize = '12px';
        printTable.style.fontFamily = 'Arial, sans-serif';
        printTable.style.direction = 'rtl';
        printTable.style.backgroundColor = 'white';

        // إنشاء div مؤقت
        const tempDiv = document.createElement('div');
        tempDiv.style.position = 'absolute';
        tempDiv.style.left = '-9999px';
        tempDiv.style.top = '0';
        tempDiv.style.backgroundColor = 'white';
        tempDiv.style.padding = '20px';
        tempDiv.style.width = '800px';

        // إضافة عنوان
        const title = document.createElement('h2');
        title.textContent = filename;
        title.style.textAlign = 'center';
        title.style.marginBottom = '20px';
        title.style.fontFamily = 'Arial, sans-serif';
        title.style.direction = 'rtl';

        tempDiv.appendChild(title);
        tempDiv.appendChild(printTable);
        document.body.appendChild(tempDiv);

        // استخدام html2canvas
        if (typeof html2canvas !== 'undefined') {
            html2canvas(tempDiv, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: 'white'
            }).then(canvas => {
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');

                const imgData = canvas.toDataURL('image/png');
                const imgWidth = 210;
                const pageHeight = 295;
                const imgHeight = (canvas.height * imgWidth) / canvas.width;
                let heightLeft = imgHeight;
                let position = 0;

                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;

                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }

                pdf.save(`${filename}.pdf`);
                document.body.removeChild(tempDiv);
                showAlert('تم تصدير PDF بنجاح', 'success');
            }).catch(error => {
                console.error('خطأ في تصدير PDF:', error);
                document.body.removeChild(tempDiv);
                fallbackPDFExport(tableId, filename);
            });
        } else {
            document.body.removeChild(tempDiv);
            fallbackPDFExport(tableId, filename);
        }

    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        fallbackPDFExport(tableId, filename);
    }
}

// طريقة بديلة لتصدير PDF
function fallbackPDFExport(tableId, filename) {
    try {
        const table = document.getElementById(tableId);
        const rows = table.getElementsByTagName('tr');

        // إنشاء محتوى نصي
        let content = filename + '\n\n';

        for (let i = 0; i < rows.length; i++) {
            const cells = rows[i].getElementsByTagName(i === 0 ? 'th' : 'td');
            let rowText = '';

            for (let j = 0; j < cells.length; j++) {
                const cellText = cells[j].textContent.trim();
                rowText += cellText + '\t';
            }

            content += rowText + '\n';
        }

        // إنشاء ملف نصي للتحميل
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${filename}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        showAlert('تم تصدير الملف كنص، يمكنك نسخه إلى برنامج معالج النصوص', 'info');

    } catch (error) {
        console.error('خطأ في التصدير البديل:', error);
        showAlert('فشل في تصدير الملف', 'error');
    }
}

// طباعة الجدول
function printTable(tableId) {
    const table = document.getElementById(tableId);
    if (!table) {
        showAlert('الجدول غير موجود', 'error');
        return;
    }

    const printWindow = window.open('', '_blank');

    // إنشاء نسخة من الجدول للطباعة
    const printTable = table.cloneNode(true);

    // إزالة أعمدة الإجراءات من الطباعة
    const actionHeaders = printTable.querySelectorAll('th');
    const actionCells = printTable.querySelectorAll('td');

    actionHeaders.forEach((header, index) => {
        if (header.textContent.includes('الإجراءات')) {
            header.remove();
            // إزالة الخلايا المقابلة
            const rows = printTable.querySelectorAll('tr');
            rows.forEach(row => {
                if (row.cells[index]) {
                    row.cells[index].remove();
                }
            });
        }
    });

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>طباعة التقرير</title>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

                body {
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                    margin: 20px;
                    font-size: 14px;
                    line-height: 1.6;
                }

                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 2px solid #333;
                    padding-bottom: 20px;
                }

                .header h1 {
                    color: #2c3e50;
                    margin: 0;
                    font-size: 24px;
                }

                .header p {
                    color: #666;
                    margin: 5px 0;
                }

                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                    font-size: 12px;
                }

                th, td {
                    border: 1px solid #ddd;
                    padding: 8px 12px;
                    text-align: right;
                    vertical-align: middle;
                }

                th {
                    background-color: #f8f9fa;
                    font-weight: bold;
                    color: #2c3e50;
                }

                tr:nth-child(even) {
                    background-color: #f9f9f9;
                }

                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    border-top: 1px solid #ddd;
                    padding-top: 20px;
                }

                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                    table { page-break-inside: auto; }
                    tr { page-break-inside: avoid; page-break-after: auto; }
                    thead { display: table-header-group; }
                    tfoot { display: table-footer-group; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>نظام محاسبة مبيعات الدراجات النارية</h1>
                <p>تقرير مطبوع - ${new Date().toLocaleDateString('ar-SA')}</p>
            </div>

            ${printTable.outerHTML}

            <div class="footer">
                <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
                <p>نظام محاسبة مبيعات الدراجات النارية - جميع الحقوق محفوظة</p>
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();

    // انتظار تحميل الخطوط ثم الطباعة
    setTimeout(() => {
        printWindow.print();
    }, 1000);
}

// التحقق من صحة النماذج
function validateForm(formId) {
    const form = document.getElementById(formId);
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

// تنظيف النماذج
function clearForm(formId) {
    const form = document.getElementById(formId);
    form.reset();
    
    // إزالة رسائل الخطأ
    const invalidInputs = form.querySelectorAll('.is-invalid');
    invalidInputs.forEach(input => {
        input.classList.remove('is-invalid');
    });
}

// تحديث الوقت الحالي
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// تشغيل تحديث الوقت كل دقيقة
setInterval(updateCurrentTime, 60000);

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الوقت الحالي
    updateCurrentTime();
    
    // إضافة تأثيرات الحركة للعناصر
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
    
    // تفعيل tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

// معالجة الأخطاء العامة
window.addEventListener('error', function(e) {
    console.error('خطأ في JavaScript:', e.error);
    showAlert('حدث خطأ في النظام. يرجى إعادة تحميل الصفحة.', 'danger');
});

// دعم PWA - تثبيت التطبيق
let deferredPrompt;

window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
    
    // إظهار زر التثبيت
    const installButton = document.getElementById('installButton');
    if (installButton) {
        installButton.style.display = 'block';
        
        installButton.addEventListener('click', () => {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    console.log('تم تثبيت التطبيق');
                }
                deferredPrompt = null;
            });
        });
    }
});

// إشعار عند تثبيت التطبيق
window.addEventListener('appinstalled', (evt) => {
    showAlert('تم تثبيت التطبيق بنجاح!', 'success');
});
