<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار الفواتير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>🧪 اختبار أزرار التعديل والتفاصيل في فواتير المبيعات</h2>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار API الفواتير</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="testGetInvoice()">
                            <i class="fas fa-file-invoice me-2"></i>
                            اختبار جلب بيانات الفاتورة
                        </button>
                        <br>
                        <button class="btn btn-warning mb-2" onclick="testEditInvoice()">
                            <i class="fas fa-edit me-2"></i>
                            اختبار تعديل الفاتورة
                        </button>
                        <br>
                        <button class="btn btn-success mb-2" onclick="testGetInstallments()">
                            <i class="fas fa-list me-2"></i>
                            اختبار جلب الأقساط
                        </button>
                        <br>
                        <button class="btn btn-info mb-2" onclick="testAddInvoice()">
                            <i class="fas fa-plus me-2"></i>
                            اختبار إضافة فاتورة
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>نتائج الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                            <p class="text-muted">اضغط على أي زر لبدء الاختبار...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاختبار:</h6>
                    <ol>
                        <li>تأكد من تشغيل الخادم على المنفذ 5000</li>
                        <li>اضغط على أزرار الاختبار أعلاه</li>
                        <li>راقب النتائج في المربع الأيمن</li>
                        <li>إذا نجحت الاختبارات، فأزرار الفواتير تعمل بشكل صحيح</li>
                        <li>للاختبار اليدوي، اذهب إلى: <a href="http://localhost:5000/invoices" target="_blank">صفحة الفواتير</a></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colorClass = {
                'info': 'text-primary',
                'success': 'text-success',
                'error': 'text-danger',
                'warning': 'text-warning'
            }[type] || 'text-dark';
            
            resultsDiv.innerHTML += `<div class="${colorClass}"><strong>[${timestamp}]</strong> ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function formatArabicNumber(number) {
            if (isNaN(number) || number === null || number === undefined) {
                return '0 ر.س';
            }
            const formattedNumber = Number(number).toLocaleString('ar-SA');
            return formattedNumber + ' ر.س';
        }

        async function testGetInvoice() {
            log('🔍 اختبار جلب بيانات الفاتورة رقم 1...', 'info');
            
            try {
                const response = await fetch('/api/invoices/1');
                log(`📡 حالة الاستجابة: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`📄 البيانات المستلمة: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.success) {
                        log('✅ تم جلب بيانات الفاتورة بنجاح', 'success');
                        log(`📋 رقم الفاتورة: ${data.invoice.invoice_number}`, 'success');
                        log(`👤 العميل: ${data.invoice.customer_name}`, 'success');
                        log(`💰 المبلغ: ${formatArabicNumber(data.invoice.total_amount)}`, 'success');
                    } else {
                        log('❌ فشل في جلب بيانات الفاتورة', 'error');
                    }
                } else {
                    log(`❌ خطأ HTTP: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            }
        }

        async function testEditInvoice() {
            log('✏️ اختبار تعديل بيانات الفاتورة...', 'info');
            
            const testData = {
                customer_id: 1,
                motorcycle_type_id: 1,
                total_amount: 25000,
                down_payment: 5000,
                remaining_amount: 20000,
                installment_amount: 1667,
                installment_count: 12,
                invoice_date: new Date().toISOString().split('T')[0]
            };
            
            try {
                const response = await fetch('/api/invoices/1', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                log(`📡 حالة الاستجابة: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`📄 البيانات المستلمة: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.success) {
                        log('✅ تم تحديث بيانات الفاتورة بنجاح', 'success');
                    } else {
                        log('❌ فشل في تحديث بيانات الفاتورة', 'error');
                    }
                } else {
                    log(`❌ خطأ HTTP: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            }
        }

        async function testGetInstallments() {
            log('📋 اختبار جلب أقساط الفاتورة...', 'info');
            
            try {
                const response = await fetch('/api/invoices/1/installments');
                log(`📡 حالة الاستجابة: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`📄 البيانات المستلمة: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.success) {
                        log('✅ تم جلب بيانات الأقساط بنجاح', 'success');
                        log(`📊 عدد الأقساط: ${data.installments.length}`, 'success');
                    } else {
                        log('❌ فشل في جلب بيانات الأقساط', 'error');
                    }
                } else {
                    log(`❌ خطأ HTTP: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            }
        }

        async function testAddInvoice() {
            log('➕ اختبار إضافة فاتورة جديدة...', 'info');
            
            const testData = {
                customer_id: 1,
                motorcycle_type_id: 1,
                total_amount: 30000,
                down_payment: 10000,
                installment_count: 12,
                invoice_date: new Date().toISOString().split('T')[0]
            };
            
            try {
                const response = await fetch('/api/invoices', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                log(`📡 حالة الاستجابة: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`📄 البيانات المستلمة: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.success) {
                        log('✅ تم إضافة الفاتورة بنجاح', 'success');
                    } else {
                        log('❌ فشل في إضافة الفاتورة', 'error');
                    }
                } else {
                    log(`❌ خطأ HTTP: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            }
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            log('🚀 بدء اختبار أزرار الفواتير...', 'info');
            log('📋 للاختبار اليدوي، اذهب إلى: http://localhost:5000/invoices', 'info');
            log('🔧 تأكد من وجود فواتير في النظام للاختبار', 'warning');
        });
    </script>
</body>
</html>
