# 🖥️ دليل تشغيل نظام محاسبة مبيعات الدراجات النارية - تطبيق سطح المكتب

## ✅ تم إنشاء تطبيق سطح المكتب بنجاح!

لقد قمت بتصميم نظام محاسبة مبيعات الدراجات النارية كتطبيق سطح مكتب مستقل **بدون الحاجة لمتصفح**.

## 🚀 كيفية التشغيل:

### الطريقة الأولى (الموصى بها):
```bash
python run_desktop.py
```

### الطريقة الثانية:
```bash
python desktop_app.py
```

## 📋 الملفات المطلوبة:

تأكد من وجود هذه الملفات في نفس المجلد:
- ✅ `desktop_app.py` - التطبيق الرئيسي
- ✅ `dialogs.py` - نوافذ الحوار
- ✅ `run_desktop.py` - ملف التشغيل

## 🎯 المميزات الرئيسية:

### ✅ **لا يحتاج متصفح:**
- تطبيق سطح مكتب مستقل
- واجهة عربية كاملة
- سرعة عالية في الاستجابة

### ✅ **إدارة العملاء:**
- إضافة عملاء جدد
- تعديل بيانات العملاء
- حذف العملاء (مع الحماية)
- ربط بالضامنين

### ✅ **إدارة الفواتير:**
- إنشاء فواتير جديدة
- عرض جميع الفواتير
- تفاصيل كاملة لكل فاتورة
- حساب تلقائي للأقساط

### ✅ **نوع الدراجة المرن:**
- **كتابة حرة** لنوع الدراجة
- **لا قوائم منسدلة معقدة**
- **إنشاء أنواع جديدة تلقائياً**
- **سهولة في الكتابة**

### ✅ **قاعدة بيانات محلية:**
- SQLite آمنة ومحلية
- لا حاجة لخادم
- بيانات تجريبية جاهزة

## 🎮 كيفية الاستخدام:

### 1. **بدء التشغيل:**
- شغل `python run_desktop.py`
- ستظهر النافذة الرئيسية
- ستجد الإحصائيات وآخر الفواتير

### 2. **إضافة عميل جديد:**
- اضغط "إدارة العملاء"
- اضغط "إضافة عميل جديد"
- املأ البيانات:
  - الاسم (مطلوب)
  - رقم الهاتف
  - العنوان
  - رقم الهوية
  - الضامن (اختياري)
- اضغط "حفظ"

### 3. **إنشاء فاتورة جديدة:**
- اضغط "إدارة الفواتير"
- اضغط "إنشاء فاتورة جديدة"
- املأ البيانات:
  - **العميل**: اختر من القائمة
  - **نوع الدراجة**: **اكتب بحرية** مثل:
    - "BMW - S1000RR"
    - "هوندا - CBR 150"
    - "ياماها - YZF-R15"
    - "دوكاتي - Panigale V4"
  - **المبلغ الإجمالي**: أدخل المبلغ
  - **المقدم**: أدخل المقدم (افتراضي 0)
  - **عدد الأقساط**: اختر من القائمة
  - **تاريخ الفاتورة**: يتم تعيينه تلقائياً
- اضغط "حفظ"

### 4. **عرض تفاصيل فاتورة:**
- في نافذة "إدارة الفواتير"
- اختر أي فاتورة من الجدول
- اضغط "عرض التفاصيل"
- ستظهر نافذة بجميع المعلومات

## 🔧 استكشاف الأخطاء:

### إذا لم يعمل التطبيق:

1. **تأكد من Python:**
```bash
python --version
```
يجب أن يكون 3.6 أو أحدث

2. **تأكد من tkinter:**
```bash
python -c "import tkinter; print('tkinter متوفر')"
```

3. **تأكد من الملفات:**
- تأكد من وجود `desktop_app.py`
- تأكد من وجود `dialogs.py`

4. **تشغيل مع تفاصيل الأخطاء:**
```bash
python -u desktop_app.py
```

### الأخطاء الشائعة:

#### ❌ "No module named 'tkinter'"
**الحل:** تثبيت Python كامل مع tkinter

#### ❌ "No module named 'dialogs'"
**الحل:** تأكد من وجود ملف `dialogs.py` في نفس المجلد

#### ❌ "Permission denied"
**الحل:** تشغيل Terminal/Command Prompt كمدير

## 🎉 المزايا مقارنة بتطبيق الويب:

### ✅ **أسرع وأبسط:**
- لا انتظار لتحميل الصفحات
- لا مشاكل متصفح
- استجابة فورية

### ✅ **أكثر أماناً:**
- البيانات محلية على جهازك
- لا تسريب عبر الإنترنت
- تحكم كامل في البيانات

### ✅ **أسهل في الاستخدام:**
- نقرة واحدة للتشغيل
- لا إعدادات معقدة
- واجهة مألوفة

### ✅ **حقل نوع الدراجة مرن:**
- **كتابة حرة بدون قيود**
- **لا قوائم منسدلة معقدة**
- **إضافة أنواع جديدة بسهولة**
- **سرعة في الإدخال**

## 📊 البيانات التجريبية:

عند التشغيل الأول، سيتم إنشاء:
- **3 عملاء تجريبيين**
- **2 ضامن تجريبي**
- **5 أنواع دراجات**
- **قاعدة بيانات فارغة للفواتير**

## 🔄 الوظائف المتاحة حالياً:

### ✅ **مكتملة وجاهزة:**
- إدارة العملاء (إضافة، تعديل، حذف)
- إنشاء الفواتير
- عرض تفاصيل الفواتير
- الإحصائيات السريعة
- حقل نوع الدراجة المرن

### 🔄 **قيد التطوير:**
- تعديل الفواتير
- سندات القبض
- التقارير المفصلة
- طباعة الفواتير

## 📁 ملفات النظام:

بعد التشغيل الأول ستجد:
- `motorcycle_accounting.db` - قاعدة البيانات المحلية

## 🎯 نصائح للاستخدام:

### ✅ **لنوع الدراجة:**
- اكتب بحرية مثل: "BMW - S1000RR"
- استخدم صيغة: "الماركة - الموديل"
- يمكنك كتابة أي نوع تريده
- لا حاجة للبحث في قوائم

### ✅ **للمبالغ:**
- أدخل الأرقام فقط
- سيتم حساب الأقساط تلقائياً
- تأكد من صحة المبالغ

### ✅ **للعملاء:**
- الاسم مطلوب
- باقي الحقول اختيارية
- يمكن ربط العميل بضامن

## 🎉 الخلاصة:

**الآن لديك نظام محاسبة مبيعات الدراجات النارية كتطبيق سطح مكتب:**

- ✅ **لا يحتاج متصفح**
- ✅ **سهل التشغيل**
- ✅ **واجهة عربية**
- ✅ **حقل نوع الدراجة مرن**
- ✅ **قاعدة بيانات محلية**
- ✅ **أمان عالي**

---

**🚀 ابدأ الآن بتشغيل: `python run_desktop.py`**
