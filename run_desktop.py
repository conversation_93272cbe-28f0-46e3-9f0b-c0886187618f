#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تشغيل تطبيق سطح المكتب لنظام محاسبة مبيعات الدراجات النارية
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from desktop_app import main
    
    print("🚀 بدء تشغيل نظام محاسبة مبيعات الدراجات النارية...")
    print("📋 تطبيق سطح المكتب")
    print("=" * 50)
    
    main()
    
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة:")
    print("- desktop_app.py")
    print("- dialogs.py")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
    import traceback
    traceback.print_exc()

input("\nاضغط Enter للخروج...")
