#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تشغيل تطبيق سطح المكتب لنظام محاسبة مبيعات الدراجات النارية
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """طباعة بانر جميل للتطبيق"""
    print("\n" + "=" * 70)
    print("🏍️  نظام محاسبة مبيعات الدراجات النارية - تطبيق سطح المكتب  🏍️")
    print("=" * 70)
    print("✨ تصميم جميل وحديث")
    print("🎨 واجهة عربية متطورة")
    print("💻 تطبيق سطح مكتب مستقل")
    print("🚀 سرعة عالية وأداء ممتاز")
    print("-" * 70)

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")

    try:
        import tkinter
        print("✅ tkinter متوفر")
    except ImportError:
        print("❌ tkinter غير متوفر")
        return False

    try:
        import sqlite3
        print("✅ sqlite3 متوفر")
    except ImportError:
        print("❌ sqlite3 غير متوفر")
        return False

    try:
        from desktop_app import MotorcycleAccountingApp
        print("✅ التطبيق الرئيسي متوفر")
    except ImportError as e:
        print(f"❌ التطبيق الرئيسي غير متوفر: {e}")
        return False

    try:
        from dialogs import CustomerDialog, InvoiceDialog
        print("✅ نوافذ الحوار متوفرة")
    except ImportError as e:
        print("❌ نوافذ الحوار غير متوفرة: {e}")
        return False

    print("✅ جميع المتطلبات متوفرة!")
    return True

try:
    print_banner()

    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        print("تأكد من وجود جميع الملفات المطلوبة:")
        print("- desktop_app.py")
        print("- dialogs.py")
        input("\nاضغط Enter للخروج...")
        sys.exit(1)

    print("\n🚀 بدء تشغيل التطبيق...")
    print("💡 ستظهر النافذة الرئيسية خلال ثوانٍ...")
    print("🎉 استمتع بالتصميم الجديد والجميل!")
    print("-" * 70)

    from desktop_app import main
    main()

except ImportError as e:
    print(f"\n❌ خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة:")
    print("- desktop_app.py")
    print("- dialogs.py")

except Exception as e:
    print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
    print("تفاصيل الخطأ:")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 70)
print("🙏 شكراً لاستخدام نظام محاسبة مبيعات الدراجات النارية")
print("=" * 70)
input("\nاضغط Enter للخروج...")
